<template>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span> 选项样式 </span>
    </div>
    <div v-if="computedOptions" class="vis-config-card__body">
      <div class="vis-form-label relative">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 列表高度 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">面板高度</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.panelHeight" icon="hticon-vis-max-height" :min="0" />
              </div>
            </div>
            <!-- 选项高度 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">选项高度</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.itemHeight" icon="hticon-vis-max-height" :min="0" />
              </div>
            </div>
          </div>
        </div>
        <!-- 文本 -->
        <vis-label v-if="computedOptions.fontStyle" title="文本" v-model="computedOptions.fontStyle" hide-toggle />

        <!-- 悬浮背景 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">悬浮样式</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="computedOptions.hoverFillPaints" :showEyes="false" :minusWidth="0" base>
                  <template #color>
                    <q-btn class="vis-field--mini" @click.stop="showHoverPopup" :class="{ active: hoverPopupShow }">
                      <q-icon
                        class="cursor-pointer vis-fill__icon hticon-vis-rect"
                        :style="hoverStyle"
                        size="14px"
                      ></q-icon>
                      <vis-popup ref="hoverPopupRef" title="悬浮样式" :target="false" @hide="hoverPopupShow = false">
                        <vis-color-selector
                          title="文本"
                          v-model="computedOptions.hoverTextFillPaints"
                          isText
                          class="!mb-2"
                        />
                        <vis-custom-color
                          v-model="computedOptions.hoverTextFillPaints"
                          colorType="text"
                          :onlyColor="true"
                        />
                        <q-separator class="!my-2" />

                        <vis-color-selector title="背景" v-model="computedOptions.hoverFillPaints" class="!mb-2" />
                        <vis-custom-color
                          v-model="computedOptions.hoverFillPaints"
                          colorType="fill"
                          :onlyColor="true"
                        />
                      </vis-popup>
                    </q-btn>
                  </template>
                </vis-fill>
              </div>
            </div>

            <!-- 选中样式 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">选中样式</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="computedOptions.activeFillPaints" :showEyes="false" :minusWidth="0" base>
                  <template #color>
                    <q-btn class="vis-field--mini" @click.stop="showActivePopup" :class="{ active: activePopupShow }">
                      <q-icon
                        class="cursor-pointer vis-fill__icon hticon-vis-rect"
                        :style="activeStyle"
                        size="14px"
                      ></q-icon>
                      <vis-popup ref="activePopupRef" title="选中样式" :target="false" @hide="activePopupShow = false">
                        <vis-color-selector
                          title="文本"
                          v-model="computedOptions.activeTextFillPaints"
                          isText
                          class="!mb-2"
                        />
                        <vis-custom-color
                          v-model="computedOptions.activeTextFillPaints"
                          colorType="text"
                          :onlyColor="true"
                        />
                        <q-separator class="!my-2" />

                        <vis-color-selector title="背景" v-model="computedOptions.activeFillPaints" class="!mb-2" />
                        <vis-custom-color
                          v-model="computedOptions.activeFillPaints"
                          colorType="fill"
                          :onlyColor="true"
                        />
                      </vis-popup>
                    </q-btn>
                  </template>
                </vis-fill>
              </div>
            </div>
          </div>
        </div>

        <!-- 圆角设置 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">圆角</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="radiusLeft"
                  icon="hticon-vis-radius-left"
                  @update:model-value="radiusChange"
                  :min="0"
                />
              </div>
            </div>
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="radiusRight"
                  icon="hticon-vis-radius-right"
                  @update:model-value="radiusChange"
                  :min="0"
                />
              </div>
            </div>
          </div>
          <q-btn flat class="btn-field" :class="{ active: showRadius }" @click="showRadius = !showRadius">
            <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
            <q-tooltip> 单独设置 </q-tooltip>
          </q-btn>
        </div>
        <template v-if="showRadius">
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="computedOptions.radius[0]" icon="hticon-vis-radius-lt" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="computedOptions.radius[1]" icon="hticon-vis-radius-rt" :min="0" />
                </div>
              </div>
            </div>
          </div>
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="computedOptions.radius[3]" icon="hticon-vis-radius-lb" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="computedOptions.radius[2]" icon="hticon-vis-radius-rb" :min="0" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./option.ts"></script>
