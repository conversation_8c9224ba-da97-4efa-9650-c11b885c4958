import { useDocumentStore } from '@vis/document-core';
import { useActionStore, useDesignStore } from '../stores';
import { useAction } from './action';
import { computed } from 'vue';
import { Keyboard } from '@hetu/platform-shared';
import { isEmpty } from 'lodash-es';

/**
 * 快捷键
 * <AUTHOR>
 */
export const useShortcutKeys = () => {
  const docStore = useDocumentStore();
  const actionStore = useActionStore();
  const designStore = useDesignStore();

  const { move, hand } = useAction();

  const active = computed(() => designStore.active.value);
  const isMetaCtrl = computed(() => actionStore.isMetaCtrl.value);
  const canvasState = computed(() => designStore.canvasState.value);

  /**
   * 添加快捷键
   */
  const addShortcuts = () => {
    registerShortcuts();

    window.addEventListener('keydown', onKeydown);
    window.addEventListener('keyup', onKeyup);

    window.addEventListener('mousedown', onMouseDown);
    window.addEventListener('mouseup', onMouseUp);
  };

  /**
   * 移除快捷键
   */
  const removeShortcuts = () => {
    window.removeEventListener('keydown', onKeydown);
    window.removeEventListener('keyup', onKeyup);

    window.removeEventListener('mousedown', onMouseDown);
    window.removeEventListener('mouseup', onMouseUp);
  };

  const onKeydown = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;
    actionStore.isShift.value = e.shiftKey;
    const target = e.target as HTMLElement;
    if (target) {
      const tagName = target.tagName;
      const exceptTags = ['INPUT', 'TEXTAREA', 'SELECT'];
      if (exceptTags.indexOf(tagName) !== -1) {
        return;
      }
      if (target.contentEditable === 'true') {
        return;
      }
    }

    // // 移动
    // const move = new Set([Keyboard.ArrowUp, Keyboard.ArrowDown, Keyboard.ArrowLeft, Keyboard.ArrowRight]);
    // // 数字键
    // const digit = new Set([
    //   Keyboard.Digit0,
    //   Keyboard.Digit1,
    //   Keyboard.Digit2,
    //   Keyboard.Digit3,
    //   Keyboard.Digit4,
    //   Keyboard.Digit5,
    //   Keyboard.Digit6,
    //   Keyboard.Digit7,
    //   Keyboard.Digit8,
    //   Keyboard.Digit9
    // ]);

    // 长按类操作：
    // 按空格键移动画布
    if (e.code === Keyboard.Space) {
      e.preventDefault();
      hand();
      return;
    }

    // 短按类操作：
    // 过滤重复值 且按照shift-ctrl-alt-key的顺序排列 确保执行正确
    const keySet = new Set([
      e.shiftKey && Keyboard.ShiftLeft,
      isMetaCtrl.value && Keyboard.ControlLeft,
      e.altKey && Keyboard.AltLeft,
      e.code
    ]);
    const keyboard = Array.from(keySet).filter(Boolean).join('-');
    // 注册快捷键
    isEmpty(canvasState.value.shortcuts) && registerShortcuts();
    // 查找快捷键
    const shortcutKey = Object.keys(canvasState.value.shortcuts).find((key) => key.split('|').includes(keyboard));
    const action = shortcutKey && canvasState.value.shortcuts[shortcutKey];
    // console.log('onKeydown:--------', keyboard, shortcutKey, action);
    if (action && !action.disable) {
      // 阻止事件的默认行为
      e.preventDefault();
      // if (move.has(e.code as any) || digit.has(e.code as any)) {
      //   (useAction() as any)[action.name] && (useAction() as any)[action.name](e.code as any);
      //   return;
      // }
      // 执行操作
      !action.loading && (useAction() as any)[action.name] && (useAction() as any)[action.name](e.code as any);
    }
  };

  const onKeyup = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;
    actionStore.isShift.value = e.shiftKey;

    // 松开空格键框选组件
    if (e.code === Keyboard.Space) {
      move();
    }
  };

  const onMouseDown = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseDown:--------', true);
    }
  };

  const onMouseUp = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseUp:--------', false);
    }
  };

  /**
   * 注册快捷键
   * */
  const registerShortcuts = () => {
    Object.keys(actionStore.actions.value).forEach((key) => {
      const action = actionStore.actions.value[key];
      // 将操作分发到所属分组中
      if (action.group) {
        actionStore.groups.value[action.group].actions[key] = action;
      }
      // 注册快捷键
      if (action.shortcuts && action.shortcuts.length > 0) {
        const keyCodes = action.shortcuts.map((shortcut) => {
          if (Array.isArray(shortcut)) {
            // 组合键：以-连接
            return shortcut.map((item) => item.toString()).join('-');
          } else {
            // 单键
            return shortcut.toString();
          }
        });
        // 以|分隔多个快捷键，表示或者关系
        canvasState.value.shortcuts[keyCodes.join('|')] = action;
      }
    });

    // console.log('canvasState.value.shortcuts:--------', canvasState.value.shortcuts);
  };

  return {
    addShortcuts,
    removeShortcuts,
    onKeydown,
    onKeyup
  };
};
