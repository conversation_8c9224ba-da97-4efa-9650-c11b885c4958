import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { Color, FillPaints, InputBoxOptions } from '@vis/document-core';
import { VisFormSize } from '../../common';

export default defineComponent({
  name: 'vis-config-form-box-style',
  components: {
    VisFormSize
  },
  props: {
    title: {
      type: String
    },
    options: {
      type: Object as PropType<InputBoxOptions>,
      required: true
    }
  },
  setup(props) {
    const computedOptions = computed(() => props.options);

    const showMenuFocus = ref(false);
    const addFocus = () => {
      computedOptions.value.focusFillPaints = new FillPaints(undefined, new Color());
      showMenuFocus.value = false;
    };

    const delFocus = () => {
      delete computedOptions.value.focusFillPaints;
    };

    //#region 圆角
    /** 圆角 */
    const radius = ref<number | string>(computedOptions.value.radius[0]);

    const radiusChange = () => {
      if (typeof radius.value === 'number') {
        computedOptions.value.radius = [radius.value, radius.value, radius.value, radius.value];
      }
    };

    const showRadius = ref(false);

    const flag = computed(
      () => !computedOptions.value.radius.every((item) => item === computedOptions.value.radius[0])
    );

    watch(
      () => flag.value,
      () => {
        radius.value = flag.value ? 'Mixed' : computedOptions.value.radius[0];
      },
      {
        immediate: true
      }
    );

    //#endregion

    return {
      computedOptions,

      showMenuFocus,
      addFocus,
      delFocus,

      radius,
      radiusChange,
      showRadius
    };
  }
});
