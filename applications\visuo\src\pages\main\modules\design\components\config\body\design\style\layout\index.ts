import {
  AutoLayout,
  Block,
  Constraints,
  ControlType,
  DirectionType,
  Frame,
  Graph,
  GRAPH_SIZE_MIN,
  GraphType,
  GridItem,
  JustifyAlign,
  ResizeType,
  TextAdapt,
  useDocumentStore,
  useLayout
} from '@vis/document-core';
import { useDesignStore } from '../../../../../../stores';
import { computed, defineComponent, nextTick, ref, watch } from 'vue';
import { useConstraints, useGraph } from '../../../../../../hooks';
import { isNumber, isString } from 'lodash-es';
import { useQuasar } from 'quasar';

/**
 * 布局
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-layout',
  props: {},
  setup() {
    const $q = useQuasar();
    const designStore = useDesignStore();
    const { document } = useDocumentStore();

    const { setChildrenPosition } = useConstraints();

    const { findGraph } = useGraph();

    const { isFlex, isGrid } = useLayout();

    // 当前选中的图形
    const graph = computed(() => {
      return designStore.active.value.graph as Graph;
    });

    // 当前选中的若是frame
    const frame = computed(() => {
      if (graph.value.type === GraphType.Frame) {
        return graph.value as Frame;
      } else {
        return undefined;
      }
    });

    // 当前选中的图形的父级Frame
    const parentFrame = computed(() => {
      if (graph.value.parent) {
        const activeFrame = designStore.active.value.frame as Frame;
        if (activeFrame && graph.value.parent === activeFrame.id) {
          return activeFrame;
        } else {
          return findGraph(graph.value.parent) as Frame;
        }
      } else {
        return undefined;
      }
    });

    //#region 布局
    const autoLayout = computed(() => frame.value?.autoLayout as AutoLayout);

    const directionOptions = [
      {
        value: DirectionType.Freeform,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Freeform}`,
        tip: '自由布局'
      },
      {
        value: DirectionType.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Horizontal}`,
        tip: '水平'
      },
      { value: DirectionType.Vertical, label: '', icon: `hticon-vis-layout-${DirectionType.Vertical}`, tip: '垂直' },
      { value: DirectionType.Grid, label: '', icon: `hticon-vis-layout-${DirectionType.Grid}`, tip: '网格' }
    ];

    /**
    /**
     * 改变布局类型
     */
    const onChangeDirection = (value: DirectionType) => {
      if ([DirectionType.Horizontal, DirectionType.Vertical].includes(value)) {
        autoLayout.value.mainAxisAlignment = JustifyAlign.Start;
        autoLayout.value.crossAxisAlignment = JustifyAlign.Start;
      }
      if (frame.value) {
        const isFreeform = autoLayout.value.direction === DirectionType.Freeform;
        const flex = isFlex(frame.value as Frame);
        const grid = isGrid(frame.value as Frame);
        if (isFreeform) {
          frame.value.limitSize.width.resize = ResizeType.Fixed;
          frame.value.limitSize.height.resize = ResizeType.Fixed;
        }

        if (grid) {
          autoLayout.value.flowWarp = false;

          if (frame.value?.children.length) {
            frame.value.autoLayout.gridSize = [1, frame.value.children.length];
            frame.value.autoLayout.gridRowsSizing = ['Auto'];
            frame.value.autoLayout.gridColumnsSizing = Array(frame.value.children.length).fill('Auto');
          }
        }

        // 非自由布局时去掉子级的约束
        frame.value?.children.forEach((g, i) => {
          g.constraints = isFreeform ? new Constraints() : undefined;
          // gird布局默认添加一行n列
          if (grid) {
            g.gridItem = new GridItem([1, 2], [i + 1, i + 2]);
            g.limitSize.width.resize = ResizeType.Fill;
            g.limitSize.height.resize = ResizeType.Fill;
          } else {
            g.gridItem = undefined;
            g.limitSize.width.resize = ResizeType.Fixed;
            g.limitSize.height.resize = ResizeType.Fixed;
          }

          if (flex || grid) {
            nextTick(() => setChildrenPosition(frame.value as Frame));
          }
        });
      }
    };

    /**
     * 更改对齐方式
     * @param hAlign 主轴对齐方式
     * @param vAlign 交叉轴对齐方式
     */
    const onHandleAlign = (hAlign: JustifyAlign, vAlign: JustifyAlign) => {
      autoLayout.value.mainAxisAlignment = hAlign;
      autoLayout.value.crossAxisAlignment = vAlign;
    };

    /**
     * 间隔改变事件
     * @param type
     * @param value
     */
    const onChangeGap = (type: 'horizontal' | 'vertical', value: number | string) => {
      if (isString(value) && value !== 'Auto') {
        (autoLayout.value as any)[type + 'Gap'] = 'Auto';
      }
    };

    //#endregion

    //#region  边距
    const paddingV = ref(0);
    const paddingH = ref(0);

    const showPadding = ref(false);

    const onChangePadding = (type: 'h' | 'v', value: number) => {
      if (type === 'v') {
        autoLayout.value.padding[0] = value;
        autoLayout.value.padding[2] = value;
      } else {
        autoLayout.value.padding[1] = value;
        autoLayout.value.padding[3] = value;
      }
    };
    //#endregion

    //#region  网格布局
    const hoverGrid = ref([0, 0]);
    const onMouseMovePicker = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.className.indexOf('hand') !== -1) {
        const row = target.getAttribute('data-row');
        const col = target.getAttribute('data-col');
        hoverGrid.value = [Number(row), Number(col)];
      }
    };

    const onMouseLeavePicker = () => {
      hoverGrid.value = [0, 0];
    };

    /**
     * 校验是否能改变行列
     * @param row
     * @param col
     * @returns
     */
    const checkGrid = (row: number, col: number) => {
      if (frame.value?.children.length) {
        const flag = frame.value.children.find((g) => {
          return g.gridItem && (g.gridItem.rows[1] - 1 > row || g.gridItem.columns[1] - 1 > col);
        });
        if (flag) {
          $q.notify({
            message: '无法从具有内容的网格中删除行或列！',
            icon: 'notification_important',
            classes: 'vis-design-notify'
          });
          return false;
        }
      }
      return true;
    };

    const onClickGrid = (row: number, col: number) => {
      if (!checkGrid(row, col)) return;

      if (row > autoLayout.value.gridSize[0]) {
        autoLayout.value.gridRowsSizing.push(...Array(row - autoLayout.value.gridSize[0]).fill('Auto'));
      } else if (row < autoLayout.value.gridSize[0]) {
        autoLayout.value.gridRowsSizing.splice(row);
      }

      if (col > autoLayout.value.gridSize[1]) {
        autoLayout.value.gridColumnsSizing.push(...Array(col - autoLayout.value.gridSize[1]).fill('Auto'));
      } else if (col < autoLayout.value.gridSize[1]) {
        autoLayout.value.gridColumnsSizing.splice(col);
      }

      autoLayout.value.gridSize = [row, col];
    };

    const onChangeGridRow = (value: number, oldValue: number) => {
      if (!checkGrid(autoLayout.value.gridSize[0], autoLayout.value.gridSize[1])) {
        nextTick(() => {
          autoLayout.value.gridSize[0] = oldValue;
        });
        return;
      }
      if (value > oldValue) {
        autoLayout.value.gridRowsSizing.push(...Array(value - oldValue).fill('Auto'));
      } else if (value < oldValue) {
        autoLayout.value.gridRowsSizing.splice(value);
      }
    };

    const onChangeGridCol = (value: number, oldValue: number) => {
      if (!checkGrid(autoLayout.value.gridSize[0], autoLayout.value.gridSize[1])) {
        nextTick(() => {
          autoLayout.value.gridSize[1] = oldValue;
        });
        return;
        return;
      }
      if (value > oldValue) {
        autoLayout.value.gridColumnsSizing.push(...Array(value - oldValue).fill('Auto'));
      } else if (value < oldValue) {
        autoLayout.value.gridColumnsSizing.splice(value);
      }
    };

    const popupRef = ref();
    const popupShow = ref(false);

    const onShowPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    const onHidePopup = () => {
      popupShow.value = false;
    };

    //#endregion

    //#region 宽高、锁定比例、调整尺寸、最大最小限制

    /** 纵横比 */
    const onAspectRatio = () => {
      graph.value.aspectRatio = !graph.value.aspectRatio;
    };

    // 获取当前选中的组件
    const activeBlock = computed(() => designStore.active.value.block);

    const isText = computed(
      () => graph.value.type === GraphType.TextBox || activeBlock.value?.type === ControlType.Paragraph
    );
    const sizeWidthDisabled = computed(() => {
      return isText.value && graph.value.text?.adapt === TextAdapt.Single;
    });
    const sizeHeightDisabled = computed(() => {
      return (
        isText.value && graph.value.text?.adapt && [TextAdapt.Single, TextAdapt.Auto].includes(graph.value.text?.adapt)
      );
    });
    const onSizeWidth = (value: number, oldValue: number) => {
      if (!isNaN(value) && !isNaN(oldValue)) {
        if (graph.value.aspectRatio) {
          const ratio = value / oldValue;
          graph.value.height = Math.round(graph.value.height * ratio);
        }

        resetAdapt();
      }
    };

    const onSizeHeight = (value: number, oldValue: number) => {
      if (!isNaN(value) && !isNaN(oldValue)) {
        if (graph.value.aspectRatio) {
          const ratio = value / oldValue;
          graph.value.width = Math.round(graph.value.width * ratio);
        }

        resetAdapt();
      }
    };

    /**
     * 重置适应方式
     */
    const resetAdapt = () => {
      if (graph.value.text) {
        graph.value.text.adapt = [TextAdapt.Single, TextAdapt.Auto].includes(graph.value.text.adapt)
          ? TextAdapt.Fixed
          : graph.value.text.adapt;
      }
    };

    /**
     * 调整尺寸
     * @param type
     */
    const onChangeResize = (type: 'width' | 'height', resizetype: ResizeType) => {
      graph.value.limitSize[type].resize = resizetype;

      // 如果父容器设置为适应内容，则子元素全部为固定尺寸
      if (resizetype === ResizeType.Adapt) {
        frame.value?.children.forEach((g) => {
          g.limitSize[type].resize = ResizeType.Fixed;
        });
      } else if (resizetype === ResizeType.Fill) {
        // 如果子元素设置为充满容器，则父容器为固定尺寸
        if (parentFrame.value) {
          parentFrame.value.limitSize[type].resize = ResizeType.Fixed;
        }
      }
    };

    // 是否显示最大最小尺寸的输入框
    const isShowSize = ref({
      width: {
        min: false,
        max: false
      },
      height: {
        min: false,
        max: false
      }
    });

    watch(
      () => graph.value.id,
      () => {
        if (graph.value) {
          isShowSize.value.width.max = isNumber(graph.value.limitSize.width.max);
          isShowSize.value.width.min = isNumber(graph.value.limitSize.width.min);
          isShowSize.value.height.max = isNumber(graph.value.limitSize.height.max);
          isShowSize.value.height.min = isNumber(graph.value.limitSize.height.min);

          if (frame.value && frame.value.autoLayout) {
            const { padding } = autoLayout.value;
            showPadding.value = !(padding[0] === padding[2] && padding[1] === padding[3]);
            if (!showPadding.value) {
              paddingV.value = padding[0];
              paddingH.value = padding[1];
            }
          }
        }
      },
      {
        immediate: true
      }
    );

    /**
     * 调整最大值最小值
     * @param key
     * @param type
     * @param value
     */
    const onChangeSize = (key: 'min' | 'max', type: 'width' | 'height', value: number | string) => {
      if (isString(value)) {
        nextTick(() => {
          graph.value.limitSize[type][key] = '';
        });
      }
    };

    /**
     * 删除某个限制
     * @param key
     * @param type
     */
    const onDeleteSize = (type: 'width' | 'height', key?: 'min' | 'max') => {
      if (key) {
        graph.value.limitSize[type][key] = '';
        isShowSize.value[type][key] = false;
      } else {
        graph.value.limitSize[type].max = '';
        isShowSize.value[type].max = false;

        graph.value.limitSize[type].min = '';
        isShowSize.value[type].min = false;
      }
      if (type === 'width') {
        showMenuWidth.value = false;
      } else {
        showMenuHeight.value = false;
      }
    };

    const showMenuWidth = ref(false);
    const showMenuHeight = ref(false);

    //#endregion
    return {
      graph,
      frame,
      parentFrame,
      isFlex,
      isGrid,
      GRAPH_SIZE_MIN,
      GraphType,

      onAspectRatio,
      sizeWidthDisabled,
      sizeHeightDisabled,
      onSizeWidth,
      onSizeHeight,

      popupRef,
      popupShow,
      onShowPopup,
      onHidePopup,

      autoLayout,
      directionOptions,
      DirectionType,
      onChangeDirection,

      JustifyAlign,
      onHandleAlign,
      onChangeGap,

      paddingH,
      paddingV,
      showPadding,
      onChangePadding,

      hoverGrid,
      onMouseMovePicker,
      onMouseLeavePicker,
      onClickGrid,
      onChangeGridRow,
      onChangeGridCol,

      ResizeType,
      onChangeResize,

      isShowSize,
      onChangeSize,
      onDeleteSize,

      showMenuWidth,
      showMenuHeight
    };
  }
});
