import { AroundPosition, Icon, RectSize } from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { useDesignStore } from '../../../../../../../../stores';
/**
 * 尺寸组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-size',
  props: {
    option: {
      type: Object as PropType<any>,
      required: true
    },
    /**
     * input - 包括输入框下拉框等框型选择器
     * button - 按钮类型
     */
    type: {
      type: String,
      default: 'input'
    }
  },
  setup(props) {
    const designStore = useDesignStore();

    const activeGraph = computed(() => designStore.active.value.graph);
    const graphWidth = computed(() => activeGraph.value!.width);
    const graphHeight = computed(() => activeGraph.value!.height);

    const computedOptions = computed(() => {
      return props.option;
    });
    const isInput = computed(() => props.type === 'input');

    const sizeMap: Record<RectSize, any> = isInput.value
      ? {
          [RectSize.Large]: {
            width: 240,
            height: 40,
            fontSize: 14
          },
          [RectSize.Medium]: {
            width: 200,
            height: 32,
            fontSize: 13
          },
          [RectSize.Small]: {
            width: 180,
            height: 28,
            fontSize: 12
          },
          [RectSize.Custom]: {}
        }
      : {
          [RectSize.Large]: {
            width: 80,
            height: 40,
            fontSize: 16
          },
          [RectSize.Medium]: {
            width: 60,
            height: 30,
            fontSize: 12
          },
          [RectSize.Small]: {
            width: 40,
            height: 20,
            fontSize: 10
          },
          [RectSize.Custom]: {}
        };

    const initSize = () => {
      const keys: RectSize[] = Object.keys(sizeMap) as RectSize[];
      const { label } = computedOptions.value;
      let labelHeight = 0,
        labelWidth = 0;
      if (label?.visible) {
        label.position === AroundPosition.Top && (labelHeight = label.fontStyle.fontSize * 1.5 + (label.gap || 0));
        label.position === AroundPosition.Left && (labelWidth = label.width + (label.gap || 0));
      }

      for (const key of keys) {
        const { width, height } = sizeMap[key];
        if (width + labelWidth === graphWidth.value && height + labelHeight === graphHeight.value) {
          return key;
        }
      }
      return RectSize.Custom;
    };
    const size = ref(initSize());
    const sizeOptions = [
      {
        label: '大',
        value: RectSize.Large
      },
      {
        label: '中',
        value: RectSize.Medium
      },
      {
        label: '小',
        value: RectSize.Small
      },
      {
        label: '自定义',
        value: RectSize.Custom
      }
    ];

    /**
     * 根据预设值调整尺寸
     * @param val
     */
    const sizeChange = (val: RectSize) => {
      // 设置宽高
      sizeMap[val].width && (activeGraph.value!.width = sizeMap[val].width);
      sizeMap[val].height && (activeGraph.value!.height = sizeMap[val].height);

      // 设置字号
      if (isInput.value && sizeMap[val].fontSize) {
        if (computedOptions.value.fontStyle) {
          computedOptions.value.fontStyle.fontSize = sizeMap[val].fontSize;
        }

        // 同步设置标签尺寸
        if (computedOptions.value.label) {
          computedOptions.value.label.fontStyle.fontSize = sizeMap[val].fontSize;

          // 标签显示时，组件调整尺寸要加入标签宽高
          if (computedOptions.value.label.visible) {
            if (computedOptions.value.label.position === AroundPosition.Left) {
              computedOptions.value.label.width = sizeMap[val].fontSize * computedOptions.value.label.text.length + 4;
              activeGraph.value!.width += computedOptions.value.label.width + (computedOptions.value.label.gap || 0);
            } else {
              computedOptions.value.label.height = sizeMap[val].fontSize * 1.5;
              activeGraph.value!.height += computedOptions.value.label.height + (computedOptions.value.label.gap || 0);
            }
          }
        }

        // 同步设置前后缀图标尺寸
        if (computedOptions.value.prefix?.length) {
          const prefixIcon = computedOptions.value.prefix.find((item: Icon) => item.type === 'icon');
          if (prefixIcon?.icon) {
            prefixIcon.icon.size = sizeMap[val].fontSize;
          }
        }
        if (computedOptions.value.suffix?.length) {
          const suffixIcon = computedOptions.value.suffix.find((item: Icon) => item.type === 'icon');
          if (suffixIcon?.icon) {
            suffixIcon.icon.size = sizeMap[val].fontSize;
          }
        }

        // 滑轨高度
        if (computedOptions.value.trackHeight && sizeMap[val].height) {
          computedOptions.value.trackHeight = sizeMap[val].height / 4;
        }

        // 设置选项高度
        if (computedOptions.value.optionStyle) {
          computedOptions.value.optionStyle.itemHeight = sizeMap[val].height;
          computedOptions.value.optionStyle.panelHeight = sizeMap[val].height * 6.25;
        }
      }
    };

    watch([() => activeGraph.value?.width, () => activeGraph.value?.height], () => {
      size.value = initSize();
    });

    return {
      size,
      sizeOptions,
      sizeChange
    };
  }
});
