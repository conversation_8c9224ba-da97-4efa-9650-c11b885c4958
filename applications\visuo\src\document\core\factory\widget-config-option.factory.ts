import {
  InputBoxOptions,
  ParagraphOptions,
  TabGroupOptions,
  Text,
  BarOptions,
  ControlType,
  ChartType,
  type WidgetType,
  NumberBoxOptions,
  SliderOptions,
  RangeOptions,
  LineOptions,
  SelectPickerOptions,
  BarMark,
  Stroke,
  Legend,
  GridPosition
} from '../models';

/**
 * 组件配置工厂类
 * 用于管理不同组件的配置
 * <AUTHOR>
 */
export class WidgetConfigOptionFactory {
  /**
   * 段落组件配置
   */
  get [ControlType.Paragraph]() {
    const options = new ParagraphOptions();
    const block = {
      text: new Text()
    };
    return {
      options,
      block
    };
  }

  /**
   * 选项卡组件配置
   */
  get [ControlType.TabGroup]() {
    const options = new TabGroupOptions();
    // const block = {
    //   text: new Text()
    // };
    return {
      options,
      block: null
    };
  }

  /**
   * 文本输入组件配置
   */
  get [ControlType.InputBox]() {
    const options = new InputBoxOptions();
    return { options, block: null };
  }

  /**
   * 数值输入组件配置
   */
  get [ControlType.NumberBox]() {
    const options = new NumberBoxOptions();
    return { options, block: null };
  }

  /**
   * 数值滑块组件配置
   */
  get [ControlType.Slider]() {
    const options = new SliderOptions();
    return { options, block: null };
  }

  /**
   * 区间滑块组件配置
   */
  get [ControlType.Range]() {
    const options = new RangeOptions();
    return { options, block: null };
  }

  /**
   * 下拉选择组件配置
   */
  get [ControlType.SelectPicker]() {
    const options = new SelectPickerOptions();
    return { options, block: null };
  }

  /**
   * 柱状图组件配置
   */
  get [ChartType.Bar]() {
    const options = new BarOptions();
    return { options, block: null };
  }

  /**
   * 堆叠柱状图组件配置
   */
  get [ChartType.StackBar]() {
    const legend: Legend = new Legend(GridPosition.CenterLeft);
    const options = new BarOptions(new BarMark(undefined, new Stroke([0, 0, 0, 0])));
    options.legend = legend;
    options.label = undefined;
    options.mark.gap = 0;
    options.mark.groupGap = 30;
    return { options, block: null };
  }

  /**
   * 折线图组件配置
   */
  get [ChartType.Line]() {
    const options = new LineOptions();
    return { options, block: null };
  }

  /**
   * 获取组件配置
   * @param widgetType 组件类型
   * @returns 组件配置
   */
  get(widgetType: WidgetType) {
    return (this as any)[widgetType];
  }
}
