import { computed, defineComponent, ref, watch, nextTick } from 'vue';
import { useDesignStore } from '../../../../stores';
import { FillPaints, Color, FillType, Text, rgbaToColor, Page, useFill, useDocumentStore } from '@vis/document-core';
import { isUndefined } from 'lodash-es';
import { AttachmentService } from '@hetu/platform-shared';
import { CacheService } from '@hetu/util';

/**
 * 页面配置面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-page',
  components: {},
  props: {},
  setup(props) {
    const doc = useDocumentStore();
    const designStore = useDesignStore();
    // #region 画布背景色
    const activePage = computed(() => designStore.active.value.page as Page);
    const infiniteCanvasRef = designStore.infiniteCanvasRef;
    const { getFillStyle } = useFill();
    const setActivePageBgColor = () => {
      nextTick(() => {
        const fillPaints = activePage.value.fillPaints;
        const canvasDom = infiniteCanvasRef.value?.getContainer();
        if (canvasDom) {
          const bgColor = fillPaints.visible ? rgbaToColor(fillPaints.color as Color) : 'transparent';
          const fill = getFillStyle(fillPaints);
          const gradient = {
            backgroundColor: fillPaints.type === FillType.Solid ? bgColor : 'unset',
            backgroundImage: fill.backgroundImage || 'unset',
            backgroundPosition: fill.backgroundPosition || 'unset',
            backgroundSize: fill.backgroundSize || 'unset',
            backgroundRepeat: fill.backgroundRepeat || 'unset'
          };
          Object.assign(canvasDom.style, gradient);
        }
      });
    };
    const onChangeFillPaints = () => setActivePageBgColor();
    // #endregion

    // #region 设置封面
    const thumbnail = ref('');
    const getThumbnailUrl = () => {
      // 接口获取
      // const id = doc.document.value.thumbnail;
      // thumbnail.value = id ? AttachmentService.downloadFileUrl(id) : '';
      // 本地获取
      CacheService.get('thumbnail').then((res) => {
        thumbnail.value = res || '';
      });
    };
    watch(
      () => doc.document.value.thumbnail,
      (val, o_val) => {
        if (val !== o_val) {
          getThumbnailUrl();
        }
      },
      {
        immediate: true
      }
    );
    // #endregion

    // #region 背景
    const themes = [
      '#F2F3FF',
      '#D9E1FF',
      '#B5C7FF',
      '#9CB4FF',
      '#7D9CFF',
      '#668AFF',
      '#557DFF',
      '#3C6AFF',
      '#365EDF',
      '#0F39C2',
      '#04279A'
    ];
    const onThemeChange = ($event: MouseEvent) => {
      const color = $event.target && ($event.target as HTMLDivElement).style.backgroundColor;
      console.log('onThemeChange', $event.target, 'bg-color:', color);
    };

    const { getPaletteColors, getRgbaFromString } = useFill();
    const palette = ref(new Color());
    const palettes = getPaletteColors(FillType.Solid);
    // console.log('palettes', palettes);
    // const palettes = ['#FF1E1E', '#FFC300', '#00B578', '#06B9B9', '#3662EC', '#8A38F5', '#5703C5', '#665A75'];
    const onPaletteChange = ($event: MouseEvent) => {
      if (!$event.target) return;
      const { r, g, b, a } = getRgbaFromString(($event.target as HTMLDivElement).style.backgroundColor);
      if (!isUndefined(r) && !isUndefined(g) && !isUndefined(b) && !isUndefined(a)) {
        palette.value = new Color(r, g, b, a);
      }
      console.log('onPaletteChange', $event.target, 'bg-color:', palette.value);
    };

    const isPaletteSelected = (color: string) => {
      const rgba = getRgbaFromString(color);
      return (
        rgba.a === palette.value.a &&
        rgba.r === palette.value.r &&
        rgba.g === palette.value.g &&
        rgba.b === palette.value.b
      );
    };

    const text = ref(new Text());
    const onChangeText = () => {
      console.log(text.value, 'text');
    };
    // #endregion
    return {
      activePage,
      onChangeFillPaints,
      thumbnail,
      themes,
      onThemeChange,
      palettes,
      palette,
      onPaletteChange,
      isPaletteSelected,
      text,
      onChangeText
    };
  }
});
