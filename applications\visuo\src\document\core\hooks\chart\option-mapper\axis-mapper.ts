import type { Axis } from '../../../models';
import {
  rgbaToColor,
  getColorValue,
  mapConfig,
  fillPaintsToG2Color,
  getLineDashFromStroke,
  calculateDataExtent,
  calculateYAxisDomain
} from './option-utils';

/**
 * 坐标轴配置映射表
 * <AUTHOR>
 */
const AXIS_MAPPING = {
  // 基础配置
  spacing: { source: 'spacing', default: 0 },

  // 标题配置
  title: {
    source: 'title.text',
    default: '',
    condition: (config: any) => config.title?.visible,
    fallback: false
  },
  titleDx: { source: 'title.offsetX', default: 0 },
  titleDy: { source: 'title.offsetY', default: 0 },
  titleTextAlign: { source: 'title.position', default: 'center' },
  titleOpacity: { source: 'title.opacity', default: 1 },
  titleAngle: { source: 'title.angle', default: 0 },
  titleSpacing: { source: 'title.gap', default: 0 },
  titlePosition: { source: 'title.position', default: 'center' },
  titleFontSize: { source: 'title.fontStyle.fontSize', default: 12 },
  titleFontFamily: { source: 'title.fontStyle.fontFamily', default: '默认字体' },
  titleFontWeight: { source: 'title.fontStyle.fontWeight', default: 400 },
  titleLineHeight: { source: 'title.fontStyle.lineHeight', default: 0 },
  titleFill: {
    source: 'title.fontStyle.fillPaints',
    transform: (value: any) => (value?.visible ? rgbaToColor(value.color) : undefined)
  },

  // 标签配置
  label: { source: 'label.visible', default: true },
  labelDx: { source: 'label.offsetX', default: 0 },
  labelDy: { source: 'label.offsetY', default: 0 },
  labelOpacity: { source: 'label.opacity', default: 1 },
  labelSpacing: { source: 'label.gap', default: 1 },
  labelFontSize: { source: 'label.fontStyle.fontSize', default: 12 },
  labelFontFamily: { source: 'label.fontStyle.fontFamily', default: '默认字体' },
  labelFontWeight: { source: 'label.fontStyle.fontWeight', default: 400 },
  labelFill: {
    source: 'label.fontStyle.fillPaints',
    transform: (value: any) => (value?.visible ? rgbaToColor(value.color) : undefined)
  },
  labelTransform: {
    source: 'label.angle',
    transform: (angle: number) => 'rotate(' + angle + ')'
  },

  // 网格线配置
  grid: {
    source: 'grid.visible',
    transform: (visible: boolean) => visible,
    condition: (config: any) => config.grid !== undefined
  },
  gridLineWidth: { source: 'grid.width', default: 0 },
  gridStroke: {
    source: 'grid.stroke.fillPaints',
    transform: (fillPaints: any) => getColorValue(fillPaints)
  },
  gridStrokeOpacity: {
    source: 'grid.stroke.fillPaints',
    transform: (fillPaints: any) => fillPaints?.color?.a || 1
  },
  gridLineDash: {
    source: 'grid.stroke',
    transform: getLineDashFromStroke
  },

  // 网格背景颜色 - 支持数组格式，第一个元素是奇数区域，第二个元素是偶数区域
  gridAreaFill: {
    source: 'grid.fillPaints',
    transform: (fillPaints: any[]) => {
      if (!fillPaints || !Array.isArray(fillPaints)) return undefined;

      const colors = fillPaints.filter((paint) => paint?.visible).map((paint) => fillPaintsToG2Color(paint));

      return colors.length > 0 ? colors : undefined;
    }
  },

  // 轴线配置
  line: {
    source: 'line.visible',
    transform: (visible: boolean) => visible,
    condition: (config: any) => config.line !== undefined
  },
  lineLineWidth: {
    source: 'line.width',
    transform: (width: number) => width / 2,
    default: 0
  },
  lineStroke: {
    source: 'line.stroke.fillPaints',
    transform: (fillPaints: any) => getColorValue(fillPaints)
  },
  lineOpacity: {
    source: 'line.stroke.fillPaints',
    transform: (fillPaints: any) => fillPaints?.color?.a || 1
  },
  lineStrokeOpacity: {
    source: 'line.stroke.fillPaints',
    transform: (fillPaints: any) => fillPaints?.color?.a || 1
  },
  lineLineDash: {
    source: 'line.stroke',
    transform: getLineDashFromStroke
  },

  // 刻度线配置
  tick: {
    source: 'tick.visible',
    transform: (visible: boolean) => visible,
    condition: (config: any) => config.tick !== undefined
  },
  tickLineWidth: { source: 'tick.width', default: 0 },
  tickLength: { source: 'tick.length', default: 0 },
  tickStroke: {
    source: 'tick.stroke.fillPaints',
    transform: (fillPaints: any) => getColorValue(fillPaints)
  },
  tickStrokeOpacity: {
    source: 'tick.stroke.fillPaints',
    transform: (fillPaints: any) => fillPaints?.color?.a || 1
  },
  tickLineDash: {
    source: 'tick.stroke',
    transform: getLineDashFromStroke
  }
} as const;

/**
 * 坐标轴配置映射器
 * <AUTHOR>
 */
export const mapAxisConfig = (axisConfig: Axis, axisType: 'x' | 'y'): Record<string, any> => {
  if (!axisConfig.visible) {
    return { axis: { [axisType]: false } };
  }

  const axisOptions: any = {
    axis: {
      [axisType]: {}
    }
  };

  const targetConfig = axisOptions.axis[axisType];

  // 映射所有配置
  mapConfig(axisConfig, targetConfig, AXIS_MAPPING);

  return axisOptions;
};

/**
 * 比例尺配置映射表
 * <AUTHOR>
 */
const SCALE_MAPPING = {
  // 比例尺类型
  type: {
    source: 'logarithmic',
    transform: (logarithmic: boolean) => (logarithmic ? 'log' : undefined)
  },

  // Y轴反转配置
  range: {
    source: 'inverse',
    transform: (inverse: boolean, config: any, axisType: string) => {
      if (axisType === 'y') {
        return inverse ? [0, 1] : [1, 0];
      }
      return undefined;
    }
  }
} as const;

/**
 * 映射比例尺配置
 * @param axisConfig 坐标轴配置
 * @param axisType 坐标轴类型
 * @param chartData 图表数据（用于计算数据范围）
 * @param yFields Y轴字段数组（用于堆叠图表计算）
 * @param chartType 图表类型（用于判断是否为堆叠图表）
 * @returns 比例尺配置
 */
export const mapScaleConfig = (
  axisConfig: Axis,
  axisType: 'x' | 'y',
  chartData?: any[],
  yFields?: string[],
  chartType?: string
): Record<string, any> => {
  if (!axisConfig.visible) {
    return {};
  }

  const scaleOptions: any = {
    [axisType]: {}
  };

  const targetConfig = scaleOptions[axisType];

  // 使用标准的映射函数处理基础配置
  mapConfig(axisConfig, targetConfig, SCALE_MAPPING, axisType);

  // 对于Y轴，需要特殊处理域范围配置
  if (axisType === 'y' && chartData && yFields && chartType) {
    // 计算数据的实际范围
    const dataExtent = calculateDataExtent(chartData, yFields, chartType);

    // 根据minExtent和maxExtent计算最终的Y轴域
    const domainConfig = calculateYAxisDomain(
      axisConfig.minExtent,
      axisConfig.maxExtent,
      dataExtent.min,
      dataExtent.max
    );

    // 应用域配置
    if (domainConfig.domain) {
      targetConfig.domain = domainConfig.domain;
    } else {
      if (domainConfig.domainMin !== undefined) {
        targetConfig.domainMin = domainConfig.domainMin;
      }
      if (domainConfig.domainMax !== undefined) {
        targetConfig.domainMax = domainConfig.domainMax;
      }
    }

    // 设置nice配置
    targetConfig.nice = domainConfig.nice;
  } else if (axisType === 'y') {
    // 如果没有数据信息，使用原有的简单逻辑
    const { minExtent, maxExtent } = axisConfig;

    if (typeof minExtent === 'number' && !isNaN(minExtent) && typeof maxExtent === 'number' && !isNaN(maxExtent)) {
      targetConfig.domain = [minExtent, maxExtent];
      targetConfig.nice = false;
    } else if (typeof minExtent === 'number' && !isNaN(minExtent)) {
      targetConfig.domainMin = minExtent;
      targetConfig.nice = false;
    } else if (typeof maxExtent === 'number' && !isNaN(maxExtent)) {
      targetConfig.domainMax = maxExtent;
      targetConfig.nice = false;
    } else {
      targetConfig.nice = true;
    }
  }

  // 如果没有任何配置，返回空对象
  if (Object.keys(targetConfig).length === 0) {
    return {};
  }

  return scaleOptions;
};

/**
 * 应用轴配置
 */
export const applyAxisConfig = (view: any, axisType: 'x' | 'y', axisConfig: any) => {
  if (!view) {
    throw new Error('图表视图未初始化');
  }

  try {
    if (axisConfig === false) {
      view.axis(axisType, false);
    } else if (axisConfig && Object.keys(axisConfig).length > 0) {
      view.axis(axisType, axisConfig);
    }
  } catch (error) {
    console.error(`应用${axisType}轴配置失败:`, error);
    throw error;
  }
};

/**
 * 应用比例尺配置
 */
export const applyScaleConfig = (view: any, scaleConfig: any) => {
  if (!view) {
    throw new Error('图表视图未初始化');
  }

  try {
    if (scaleConfig && Object.keys(scaleConfig).length > 0) {
      Object.keys(scaleConfig).forEach((axisType) => {
        view.scale(axisType, scaleConfig[axisType]);
      });
    }
  } catch (error) {
    console.error('应用比例尺配置失败:', error);
    throw error;
  }
};

/**
 * 应用坐标系配置
 */
export const applyCoordinateConfig = (chart: any, coordinateConfig: any) => {
  if (!chart) {
    throw new Error('图表实例未初始化');
  }

  try {
    if (coordinateConfig && coordinateConfig.transform) {
      // 通过 chart.options 方法应用坐标系配置
      chart.options({
        coordinate: coordinateConfig
      });
    }
  } catch (error) {
    console.error('应用坐标系配置失败:', error);
    throw error;
  }
};
