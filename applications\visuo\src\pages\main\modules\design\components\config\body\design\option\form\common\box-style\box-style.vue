<template>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span> {{ title || '样式' }} </span>
      <q-btn v-if="!computedOptions.focusFillPaints" flat dense>
        <ht-icon class="vis-icon" name="vis-add" />
        <q-menu v-model="showMenuFocus" style="width: 66px" class="vis-menu" dense>
          <q-list dense>
            <q-item :disable="!!computedOptions.focusFillPaints" @click="addFocus" clickable v-close-popup>
              <q-item-section>焦点</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>
    <div class="vis-config-card__body">
      <!-- 尺寸 -->
      <vis-form-size :option="computedOptions" />

      <!-- 文本 -->
      <vis-label v-if="computedOptions.fontStyle" title="文本" v-model="computedOptions.fontStyle" hide-toggle />

      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-0">
          <!-- 外观 -->
          <div class="vis-form-field">
            <div class="vis-form-field__label">外观</div>
            <div class="vis-form-field__content">
              <vis-fill v-model="computedOptions.fillPaints" base :showEyes="false" :minusWidth="0">
                <template #color>
                  <vis-surface
                    v-model:fillPaints="computedOptions.fillPaints"
                    v-model:stroke="computedOptions.stroke"
                  />
                </template>
              </vis-fill>
            </div>
          </div>
          <!-- 圆角 -->
          <div class="vis-form-field">
            <div class="vis-form-field__label">圆角</div>
            <div class="vis-form-field__content">
              <vis-mix-input v-model="radius" icon="hticon-vis-radius" @update:model-value="radiusChange" :min="0" />
            </div>
          </div>

          <q-btn flat :class="{ active: showRadius }" @click="showRadius = !showRadius">
            <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
            <q-tooltip> 单独设置 </q-tooltip>
          </q-btn>
        </div>
      </div>
      <!-- 单独圆角设置 -->
      <template v-if="showRadius">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.radius[0]" icon="hticon-vis-radius-lt" :min="0" />
              </div>
            </div>
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.radius[1]" icon="hticon-vis-radius-rt" :min="0" />
              </div>
            </div>
          </div>
        </div>
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.radius[3]" icon="hticon-vis-radius-lb" :min="0" />
              </div>
            </div>
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.radius[2]" icon="hticon-vis-radius-rb" :min="0" />
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 获取焦点后的边框颜色 -->
      <div v-if="computedOptions.focusFillPaints" class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">焦点</div>
            <div class="vis-form-field__content">
              <vis-fill v-model="computedOptions.focusFillPaints" />
            </div>
          </div>
        </div>
        <q-btn class="btn-field" @click="delFocus">
          <ht-icon class="vis-icon" name="vis-subtract" />
        </q-btn>
      </div>

      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <!-- 占位符颜色 -->
          <div class="vis-form-field">
            <div class="vis-form-field__label">占位符</div>
            <div class="vis-form-field__content">
              <vis-fill
                v-model="computedOptions.placeholderFillPaints"
                :minusWidth="0"
                onlyColor
                :showEyes="false"
              ></vis-fill>
            </div>
          </div>
        </div>
      </div>
      <!-- 清除按钮 -->
      <div class="vis-form-inline">
        <div class="vis-form-field">
          <div class="vis-form-field__label"></div>
          <div class="vis-form-field__content">
            <q-checkbox v-model="computedOptions.clearable" label="显示清除按钮" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./box-style.ts"></script>
