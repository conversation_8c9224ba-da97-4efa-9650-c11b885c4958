<template>
  <div class="vis-config-number-box-option">
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 基础 </span>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 默认值 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">默认值</div>
              <div class="vis-form-field__content">
                <q-input
                  v-model="computedOptions.defaultValue"
                  type="number"
                  placeholder="默认值"
                  flat
                  borderless
                  class="vis-input w-full rounded-borders px-2 no-spin"
                  :min="minValue"
                  :max="maxValue"
                  @blur="handleCheck"
                  @keydown.enter="handleCheck"
                />
              </div>
            </div>
            <!-- 占位符 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">占位符</div>
              <div class="vis-form-field__content">
                <q-input
                  v-model="computedOptions.placeholder"
                  placeholder="占位符"
                  flat
                  borderless
                  class="vis-input w-full rounded-borders px-2"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 格式化 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">格式</div>
              <div class="vis-form-field__content">
                <vis-select v-model="computedOptions.format" :options="formatOptions" />
              </div>
            </div>
            <!-- 步长 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">步长</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.step" :min="0" icon="hticon-vis-max-width" />
              </div>
            </div>
          </div>
        </div>
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 小数点 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">小数点</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.decimal" :min="0" icon="hticon-vis-max-width" />
              </div>
            </div>
            <!-- 千分位 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">千分位</div>
              <div class="vis-form-field__content">
                <q-checkbox v-model="computedOptions.thousands" label="显示千分位" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <q-separator />

    <!-- 样式设置 -->
    <vis-form-box-style :options="computedOptions" />
    <q-separator />

    <!-- 标签设置 -->
    <vis-form-label :options="options.label" @handleManage="labelManage" />
    <q-separator />

    <!-- 前后缀 -->
    <vis-form-fix :prefix="computedOptions.prefix" :suffix="computedOptions.suffix" :fixList="fixList"></vis-form-fix>
    <q-separator />

    <!-- 校验规则 -->
    <vis-form-rules :options="computedOptions.rules" :ruleList="ruleList"></vis-form-rules>
  </div>
</template>
<script lang="ts" src="./number-box.ts"></script>
