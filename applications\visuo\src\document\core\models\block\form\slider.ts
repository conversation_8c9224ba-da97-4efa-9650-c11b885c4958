import { FormFix, Label } from '.';
import { AroundPosition, Color, Effects, FillPaints, FillType, Font, Stroke } from '../../ui';
import { ControlType } from '../config/block-enum';
import { WidgetBlock } from '../widget-block';

/**
 * 数值滑块
 * <AUTHOR>
 */
export class Slider extends WidgetBlock {
  type = ControlType.Slider;

  options: SliderOptions = new SliderOptions();
}

export class SliderOptions {
  /** 标签 */
  label?: Label;
  /** 默认值 */
  defaultValue = 50;
  /** 步长 */
  step = 1;
  /** 最小值 */
  min = 0;
  /** 最大值 */
  max = 100;
  /** 字体-用于前后缀和标签文字 */
  fontStyle: Font = new Font(13);
  /** 滑轨高度 */
  trackHeight = 8;
  /** 滑轨颜色-滑过的颜色 */
  trackFillPaints = new FillPaints(undefined, new Color(21, 100, 254));
  /** 滑轨背景 */
  fillPaints = new FillPaints(undefined, new Color(215, 215, 215));
  /** 按钮外观 */
  thumb = new SliderThumb();
  /** 按钮标签 */
  thumbLabel = new SliderThumbLabel();
  /** 前缀 */
  prefix: FormFix[] = [];
  /** 后缀 */
  suffix: FormFix[] = [];
}

/** 按钮 */
export class SliderThumb {
  fillPaints = new FillPaints(undefined, new Color(21, 100, 254));
  stroke = new Stroke([1, 1, 1, 1], new FillPaints(FillType.Solid, new Color(255, 255, 255)));
  effects = new Effects(false);
}

/** 按钮标签 */
export class SliderThumbLabel {
  /** 标签后缀文本 */
  suffix = '';
  /** 标签位置 */
  position: AroundPosition.Right | AroundPosition.Top = AroundPosition.Right;
  /** 文本颜色 */
  textFillPaints = new FillPaints(undefined, new Color(0, 0, 0));
  /** 文本背景 */
  fillPaints = new FillPaints();
}
