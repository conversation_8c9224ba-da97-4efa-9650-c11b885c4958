import {
  type Mark,
  type ChartOptions,
  type FillPaints,
  ChartType,
  type ChartOptionsType,
  StrokeType,
  FillType,
  type ChartMark
} from '../../../models';
import {
  rgbaToColor,
  getColorValue,
  mapConfig,
  getLineDashFromStroke,
  mapBarConfig,
  applyBarStyle,
  mapLineConfig,
  applyLineStyle
} from '.';

export const chartFactoryConfigMap: Record<
  string,
  (markConfig: any, type: string, options?: ChartOptionsType, seriesIndex?: number) => Record<string, any>
> = {
  [ChartType.Bar]: mapBarConfig,
  [ChartType.StackBar]: mapBarConfig,
  [ChartType.Line]: mapLineConfig
};

/**
 * 方法映射
 */
export const applyMarkStyleMap: Record<
  string,
  (
    chart: any,
    view: any,
    styleConfig: any,
    index: number,
    type: ChartType,
    mark: any,
    xField?: string,
    yField?: string
  ) => void
> = {
  [ChartType.Bar]: applyBarStyle,
  [ChartType.StackBar]: applyBarStyle,
  [ChartType.Line]: applyLineStyle
};

/**
 * 标记配置映射表
 * <AUTHOR>
 */
const MARK_MAPPING = {
  opacity: { source: 'opacity', default: 1 },

  // 填充样式
  fill: {
    source: 'fillPaints',
    transform: (fillPaints: FillPaints, config: any, options: any, seriesIndex?: number) => {
      // 单系列情况：使用全局 fillPaints
      if (options.mark.series.length === 1) {
        return getColorValue(options.mark.fillPaints);
      }

      // 多系列情况：根据系列索引获取对应颜色
      if (seriesIndex !== undefined && options.mark.series[seriesIndex]) {
        return getColorValue(options.mark.series[seriesIndex].fillPaints);
      }

      return (d: any, index: any, data: any, column: any) => {
        const keys = Object.keys(d).map((key) => key + '-' + d[key]);
        const series = options.mark.series.find((item: any) => keys.includes(item.id));
        return getColorValue(series?.fillPaints);
      };
    }
  },
  fillOpacity: {
    source: 'fillPaints',
    transform: (fillPaints: FillPaints) => fillPaints?.color?.a || 1
  },

  // 描边样式
  stroke: {
    source: 'stroke.fillPaints',
    transform: (fillPaints: FillPaints, config: any, options: ChartOptionsType, seriesIndex?: number) => {
      // 单系列情况：使用全局 fillPaints
      if (options?.mark?.series.length === 1) {
        return getColorValue(options.mark.stroke.fillPaints);
      }

      // 多系列情况：根据系列索引获取对应颜色
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return getColorValue(options.mark.series[seriesIndex].stroke.fillPaints);
      }

      return (d: any, index: any, data: any, column: any) => {
        const keys = Object.keys(d).map((key) => key + '-' + d[key]);
        const series = options.mark.series.find((item: any) => keys.includes(item.id));
        return getColorValue(series?.stroke.fillPaints);
      };
    }
  },
  strokeOpacity: {
    source: 'stroke.fillPaints.color.a',
    transform: (a: number, config: any, options: ChartOptionsType, seriesIndex?: number) => {
      // 折线图宽度为0或不显示时，透明度为0
      if (
        options.mark.stroke.position[0] === 0 ||
        options.mark.stroke.style === StrokeType.None ||
        (!options.mark.stroke.fillPaints.color && options.mark.stroke.fillPaints.type === FillType.Solid)
      )
        return 0;

      // 单系列情况：使用全局配置
      if (options?.mark?.series.length === 1) {
        return options.mark.stroke.fillPaints?.color?.a || 1;
      }

      // 多系列情况：根据系列索引获取对应透明度
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return options.mark.series[seriesIndex].stroke.fillPaints?.color?.a || 1;
      }

      return a || 1;
    }
  },
  lineWidth: {
    source: 'stroke.position.0',
    transform: (width: number, config: any, options: ChartOptionsType, seriesIndex?: number) => {
      // 单系列情况：使用全局配置
      if (options?.mark?.series.length === 1) {
        return options.mark.stroke.position?.[0] || 0;
      }

      // 多系列情况：根据系列索引获取对应线宽
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return options.mark.series[seriesIndex].stroke.position?.[0] || 0;
      }

      return width || 1;
    }
  },
  lineDash: {
    source: 'stroke',
    transform: (stroke: any, config: any, options: ChartOptionsType, seriesIndex?: number) => {
      // 单系列情况：使用全局配置
      if (options?.mark?.series.length === 1) {
        return getLineDashFromStroke(options.mark.stroke);
      }

      // 多系列情况：根据系列索引获取对应线型
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return getLineDashFromStroke(options.mark.series[seriesIndex].stroke);
      }

      return getLineDashFromStroke(stroke);
    }
  },

  // 阴影效果
  shadowBlur: {
    source: 'effects.blur',
    default: 4,
    condition: (config: any) => config.effects?.visible && config.effects?.type === 'outset'
  },
  shadowColor: {
    source: 'effects.color',
    transform: (color: any, config: any) =>
      config.effects?.visible && config.effects?.type === 'outset' ? rgbaToColor(color) : undefined
  },
  shadowOffsetX: {
    source: 'effects.offset.x',
    default: 0,
    condition: (config: any) => config.effects?.visible && config.effects?.type === 'outset'
  },
  shadowOffsetY: {
    source: 'effects.offset.y',
    default: 4,
    condition: (config: any) => config.effects?.visible && config.effects?.type === 'outset'
  }
} as const;

/**
 * 标记配置映射器
 */
export const mapMarkConfig = (markConfig: Mark, options?: ChartOptions, seriesIndex?: number): Record<string, any> => {
  const markOptions: any = {};

  // 映射所有配置
  mapConfig(markConfig, markOptions, MARK_MAPPING, options, seriesIndex);

  return markOptions;
};

export const mapMarkStyle = (
  markConfig: Mark,
  type: string,
  options?: ChartOptionsType,
  seriesIndex?: number
): Record<string, any> => {
  const mapFn = chartFactoryConfigMap[type as string];
  if (mapFn) {
    return mapFn(markConfig, type, options, seriesIndex);
  }
  return {};
};

/**
 * 应用图形图样式配置
 */
export const applyMarkStyle = (
  chart: any,
  view: any,
  mark: any,
  index: number,
  type: ChartType,
  styleConfig: any,
  xField?: string,
  yField?: string
) => {
  if (!mark || !styleConfig) {
    return;
  }

  try {
    mark.attr('zIndex', 0);
    Object.entries(styleConfig).forEach(([property, value]) => {
      if (value !== undefined && value !== null) {
        // 跳过特殊属性，不应用到mark上
        if (property.startsWith('_')) {
          return;
        }
        mark.style(property, value);
      }
    });

    const mapFn = applyMarkStyleMap[type as string];
    if (mapFn) {
      mapFn(chart, view, styleConfig, index, type, mark, xField, yField);
    }
  } catch (error) {
    console.error('应用图形样式配置失败:', error);
    throw error;
  }
};
