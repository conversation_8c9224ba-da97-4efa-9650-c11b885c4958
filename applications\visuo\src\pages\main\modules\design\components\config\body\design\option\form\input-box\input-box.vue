<template>
  <div class="vis-config-input-box-option">
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 基础 </span>
      </div>
      <div class="vis-config-card__body">
        <!-- 默认值 -->
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">默认值</div>
            <div class="vis-form-field__content">
              <q-input
                v-model="computedOptions.defaultValue"
                placeholder="请输入默认值"
                flat
                borderless
                class="vis-input w-full rounded-borders px-2"
              />
            </div>
          </div>
        </div>
        <!-- 占位符 -->
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">占位符</div>
            <div class="vis-form-field__content">
              <q-input
                v-model="computedOptions.placeholder"
                placeholder="请输入占位符"
                flat
                borderless
                class="vis-input w-full rounded-borders px-2"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <q-separator />

    <!-- 样式设置 -->
    <vis-form-box-style :options="computedOptions" />
    <q-separator />

    <!-- 标签设置 -->
    <vis-form-label :options="options.label" @handleManage="labelManage" />
    <q-separator />

    <!-- 前后缀 -->
    <vis-form-fix :prefix="computedOptions.prefix" :suffix="computedOptions.suffix" :fixList="fixList"></vis-form-fix>
    <q-separator />

    <!-- 校验规则 -->
    <vis-form-rules :options="computedOptions.rules" :ruleList="ruleList"></vis-form-rules>
  </div>
</template>
<script lang="ts" src="./input-box.ts"></script>
