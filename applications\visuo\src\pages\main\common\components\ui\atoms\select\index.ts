import { computed, defineComponent, readonly, ref, watch } from 'vue';
import type { QSelectOption } from 'quasar';

/**
 * 下拉框
 * 允许输入添加下拉选项
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-select',
  props: {
    modelValue: {
      type: [String, Number, null, Array<String | Number | null>],
      required: true
    },
    options: {
      type: Array<string | number | QSelectOption>,
      required: true
    },
    /**
     * 下拉选项是否可编辑
     */
    editable: {
      type: Boolean,
      default: false
    },
    /**
     * 弹窗的最大高度
     */
    popHeight: {
      type: [Number, String],
      default: 240
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const selectRef = ref();
    const value = ref(props.modelValue);
    watch(
      () => props.modelValue,
      (val) => {
        value.value = val;
      }
    );

    const propOptions = computed(() => props.options);

    const handleUpdate = (val: string | number) => {
      emit('update:modelValue', val);
      emit('change', val);
    };

    /**
     * handleClick配合isMenuOpen阻止输入框点击时弹出下拉菜单
     */
    const isMenuOpen = ref(false);
    const handleClick = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (props.editable) {
        // 点击输入框时隐藏下拉选项
        selectRef.value.hidePopup();
      } else {
        if (isMenuOpen.value) {
          selectRef.value.hidePopup();
        } else {
          selectRef.value.showPopup();
        }
      }
    };

    /**
     * 处理添加新选项并选中
     * @param val
     */
    const handleNewValue = (val: string) => {
      const idx = propOptions.value.findIndex((item) => {
        if (typeof item === 'object') {
          return `${item.value}` === `${val}` || `${item.label}` === `${val}`;
        }
        return `${item}` === val;
      });

      let res: number | string = val;
      if (idx === -1) {
        if (typeof propOptions.value[0] === 'object') {
          propOptions.value.push({ value: val, label: val });
        } else {
          propOptions.value.push(val);
        }
        emit('add', val);
      } else {
        res =
          typeof propOptions.value[idx] === 'object'
            ? (propOptions.value[idx] as QSelectOption).value
            : propOptions.value[idx];
      }
      value.value = res;
      handleUpdate(res);
    };

    const computedStyles = computed(() => {
      return {
        '--popup-max-height': isNaN(parseInt(`${props.popHeight}`))
          ? props.popHeight
          : parseInt(`${props.popHeight}`) + 'px'
      };
    });

    return {
      selectRef,
      value,
      handleUpdate,
      propOptions,
      isMenuOpen,
      handleClick,
      handleNewValue,

      computedStyles
    };
  }
});
