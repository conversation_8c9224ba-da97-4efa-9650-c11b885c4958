import { isBoolean, isNumber, isString } from 'lodash-es';
import { throttle } from 'quasar';
import { computed, defineComponent, onMounted, ref, watch, nextTick } from 'vue';
import { useNumber } from '../../../../hooks';

/**
 * 数字输入框
 * 支持min、max设置范围
 * 支持diasble设置禁用状态
 * 在图标上拖动可改变数值
 * 支持后缀符号跟随数字位置
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-number',
  props: {
    modelValue: {
      type: [Number, String],
      required: true
    },
    icon: {
      type: String
    },
    // 后缀符号（如度数符号°）
    suffix: {
      type: String
    },
    // 数值框的最小值和最大值
    min: {
      type: Number
    },
    max: {
      type: Number
    },
    // 步长
    step: {
      type: Number
    },
    // 节流控制
    throttle: {
      type: Number,
      default: 0
    },
    tooltip: {
      type: String
    },
    disabled: {
      type: Boolean
    },
    readonly: {
      type: Boolean
    },
    /**
     * 数值精度
     * 例如值为2，则保留小数点后两位
     */
    precision: {
      type: [Number, String]
    },
    // 是否显示百分比切换
    percentage: {
      type: Boolean,
      default: false
    },
    /** 占位字符串，输入非数值时显示 */
    placeStr: {
      type: String
    },
    /** 占位字符串回调函数: 返回true时，显示输入的值, 返回非boolean时，显示返回的值 */
    callbackPlace: {
      type: Function
    }
  },
  // 指令阻止原生change事件
  directives: {
    'stop-change': {
      mounted(el) {
        const input = el.querySelector('input');
        input.addEventListener(
          'change',
          (e: Event) => {
            e.stopImmediatePropagation();
            e.preventDefault();
          },
          true
        );
      }
    }
  },
  setup(props, { emit }) {
    // 调整灵敏度（值越小，拖动变化越慢）
    const sensitivity = 0.5;

    const numberInput = ref();
    const suffixElement = ref();
    const suffixStyle = ref({ left: '0px' });

    const { getValue } = useNumber();

    const showSuffix = computed(() => {
      if (props.callbackPlace) {
        return props.callbackPlace(props.modelValue) === true;
      }
      return true;
    });
    watch(
      () => props.modelValue,
      (val) => {
        numberValue.value = getValue(val, props.precision);

        nextTick(() => {
          updateSuffixPosition();
        });
      }
    );

    const isPercentage = ref(props.percentage ? props.modelValue.toString().includes('%') : false);
    const suffixValue = ref(isPercentage.value ? '%' : props.suffix);
    const minValue = ref(isNumber(props.min) ? props.min : isPercentage.value ? 0 : -Infinity);
    const maxValue = ref(isNumber(props.max) ? props.max : isPercentage.value ? 100 : Infinity);

    watch([() => props.min, () => props.max], () => {
      minValue.value = isNumber(props.min) ? props.min : isPercentage.value ? 0 : -Infinity;
      maxValue.value = isNumber(props.max) ? props.max : isPercentage.value ? 100 : Infinity;
    });

    const isDragging = ref(false);

    const numberValue = ref(getValue(props.modelValue, props.precision));

    // 计算文本宽度的辅助函数
    const getTextWidth = (text: string, font: string): number => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      if (context) {
        context.font = font;
        return context.measureText(text).width;
      }
      return 0;
    };

    // 更新后缀符号位置
    const updateSuffixPosition = () => {
      if (!suffixValue.value || !numberInput.value) return;

      nextTick(() => {
        const inputElement = numberInput.value.$el.querySelector('input');
        if (!inputElement) return;

        const inputValue = String(numberValue.value).replace('%', '');
        if (!inputValue) {
          // 如果没有输入值，将符号放在输入框开始位置
          const computedStyle = window.getComputedStyle(inputElement);
          const paddingLeft = parseInt(computedStyle.paddingLeft) || 0;
          const prependWidth = props.icon ? 24 : 0;
          suffixStyle.value = { left: `${paddingLeft + prependWidth}px` };
          return;
        }

        const computedStyle = window.getComputedStyle(inputElement);
        const font = `${computedStyle.fontWeight} ${computedStyle.fontSize} ${computedStyle.fontFamily}`;

        // 计算文本宽度
        const textWidth = getTextWidth(inputValue, font);

        // 获取输入框的padding-left
        const paddingLeft = parseInt(computedStyle.paddingLeft) || 0;

        // 计算前置图标的宽度
        const prependElement = numberInput.value.$el.querySelector('.q-field__prepend');
        const prependWidth = prependElement ? prependElement.offsetWidth : 0;

        // 设置后缀位置（紧贴数字后面）
        const leftPosition = paddingLeft + prependWidth + textWidth + 8;
        suffixStyle.value = { left: `${leftPosition}px` };
      });
    };

    const handleUpdate = (val: string | number, type?: string) => {
      // 处理占位字符串
      if (isString(val) && isNaN(Number(val)) && props.placeStr) {
        numberValue.value = props.placeStr;
      }
      if (props.callbackPlace) {
        const flag = props.callbackPlace(val);
        if (!isBoolean(flag)) {
          numberValue.value = flag;
          val = isNaN(flag) ? val : flag;
        }
      }

      const newValue = getValue(Number(val), props.precision);

      if (numberValue.value !== props.placeStr) {
        numberValue.value = newValue;
      }
      if (isNaN(Number(val))) {
        return;
      }
      emit('update:modelValue', isPercentage.value ? `${newValue}%` : Number(newValue));

      // 触发change事件
      if (`${val}` === `${props.modelValue}`) return;
      emit(
        'change',
        isPercentage.value ? `${newValue}%` : Number(newValue),
        isPercentage.value
          ? `${getValue(props.modelValue, props.precision)}%`
          : Number(getValue(Number(props.modelValue), props.precision)),
        type
      );

      // 更新后缀位置
      nextTick(() => {
        updateSuffixPosition();
      });
    };

    const isIconFont = computed(() => {
      return props.icon?.startsWith('vis') || props.icon?.startsWith('hticon-vis');
    });

    // #region 拖动图标改变数值
    onMounted(() => {
      // 初始化后缀位置
      updateSuffixPosition();

      // 添加输入事件监听器来实时更新符号位置
      if (props.suffix) {
        const inputElement = numberInput.value.$el.querySelector('input');
        if (inputElement) {
          inputElement.addEventListener('input', updateSuffixPosition);
          inputElement.addEventListener('keyup', updateSuffixPosition);
          inputElement.addEventListener('paste', () => {
            setTimeout(updateSuffixPosition, 0);
          });
        }
      }

      const startX = ref(0);
      const startValue = ref(0);
      const oldX = ref(0);

      nextTick(() => {
        // 查找到当前输入框的图标
        const dragHandle = numberInput.value.$el.querySelector('.drag-icon');
        // 鼠标按下时开始监听拖动
        dragHandle?.addEventListener('mousedown', (e: any) => {
          if (props.readonly) return;

          isDragging.value = true;
          startX.value = e.clientX;
          startValue.value = Number(numberValue.value) || 0;

          document.addEventListener('mousemove', handleDrag);
          document.addEventListener('mouseup', stopDrag);

          // 防止选中文本
          e.preventDefault();
        });
      });

      // 鼠标移动时计算拖动的距离并更新数值
      const handleDrag = throttle((e: MouseEvent) => {
        if (!isDragging.value) return;

        // 保持鼠标样式
        document.body.classList.add('dragging');

        const deltaX = e.clientX - startX.value;
        const newValue =
          props.step && isNumber(props.step)
            ? parseFloat((startValue.value + deltaX * sensitivity * props.step).toFixed(2))
            : Math.ceil(startValue.value + deltaX * sensitivity);

        // 确保数值在 min 和 max 范围内
        const min = minValue.value !== undefined ? minValue.value : -Infinity;
        const max = maxValue.value !== undefined ? maxValue.value : Infinity;
        const formatValue = getValue(Math.min(max, Math.max(min, newValue)), props.precision);

        if (props.callbackPlace) {
          // 多值只抛出change事件
          const flag = props.callbackPlace(formatValue);
          if (!isBoolean(flag)) {
            const distance =
              props.step && Number.isInteger(props.step)
                ? parseFloat((deltaX * sensitivity * props.step).toFixed(2))
                : Math.ceil(deltaX * sensitivity);

            if (distance !== oldX.value) {
              emit('change', distance, oldX.value, 'drag');
              oldX.value = distance as number;
            }
          } else {
            nextTick(() => {
              handleUpdate(formatValue, 'drag');
            });
          }
        } else {
          nextTick(() => {
            handleUpdate(formatValue, 'drag');
          });
        }
      }, props.throttle);

      // 鼠标松开时停止监听
      const stopDrag = () => {
        document.body.classList.remove('dragging');

        oldX.value = 0;
        isDragging.value = false;
        document.removeEventListener('mousemove', handleDrag);
        document.removeEventListener('mouseup', stopDrag);
      };
    });

    // #endregion

    const oldValue = ref(numberValue.value);
    const handleFocus = () => {
      oldValue.value = numberValue.value;
      numberInput.value && numberInput.value.select();
    };

    const handleBlur = () => {
      if (numberValue.value === undefined) {
        numberValue.value = getValue(Number(oldValue.value), props.precision);
      } else if (minValue.value !== undefined && Number(numberValue.value) < minValue.value) {
        numberValue.value = getValue(minValue.value, props.precision);
      } else if (maxValue.value !== undefined && Number(numberValue.value) > maxValue.value) {
        numberValue.value = getValue(maxValue.value, props.precision);
      }
      oldValue.value = numberValue.value;

      handleUpdate(numberValue.value, 'blur');
    };

    // #region 切换数值和百分比
    const showMenu = ref(false);
    const changeType = (type: 'number' | 'percentage') => {
      isPercentage.value = type === 'percentage';
      handleUpdate(numberValue.value);
    };

    watch(
      () => isPercentage.value,
      () => {
        suffixValue.value = isPercentage.value ? '%' : props.suffix;
        minValue.value = isNumber(props.min) ? props.min : isPercentage.value ? 0 : -Infinity;
        maxValue.value = isNumber(props.max) ? props.max : isPercentage.value ? 100 : Infinity;
      }
    );

    // #endregion

    return {
      numberInput,
      suffixElement,
      showSuffix,
      numberValue,
      suffixStyle,
      handleUpdate,
      updateSuffixPosition,
      isIconFont,

      suffixValue,

      handleFocus,
      handleBlur,

      showMenu,
      isPercentage,
      changeType
    };
  }
});
