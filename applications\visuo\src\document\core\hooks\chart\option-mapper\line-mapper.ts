import { DocumentService } from '../../../services';
import {
  AdornType,
  Chart,
  Effects,
  FillPaints,
  FillType,
  ImageType,
  Interpolate,
  Mark,
  type ChartMark,
  type ChartOptionsType,
  type LineMark
} from '../../../models';
import { getColorValue, mapConfig } from './option-utils';
import { AttachmentService } from '@hetu/platform-shared';
import { register } from '@antv/g2';
import { useSvgParser } from '../../svg-parser';

/**
 * 折线图配置映射表
 * <AUTHOR>
 */
const LINE_MAPPING = {
  // 折线填充去除
  fillOpacity: {
    source: 'fillPaints',
    transform: () => 0
  },
  // 折线类型配置
  shape: {
    source: 'interpolate',
    transform: (interpolate: Interpolate) => {
      if (interpolate === Interpolate.Line) {
        return 'line';
      } else if (interpolate === Interpolate.Curve) {
        return 'smooth';
      } else if (interpolate === Interpolate.Step) {
        return 'vh';
      }
    }
  },

  // 标记类型
  _mark: {
    source: 'shape'
  },
  // 标记类型
  _shapeType: {
    source: 'shape.type'
  },
  // 标记填充
  _shapeFill: {
    source: 'shapeFillPaints',
    transform(fillPaints: FillPaints, config: any, type: string, options: any, seriesIndex?: number) {
      if (!fillPaints.color && fillPaints.type === FillType.Solid) {
        // 单系列情况：使用全局 fillPaints
        if (options.mark.series.length === 1) {
          return getColorValue(options.mark.fillPaints);
        }

        // 多系列情况：根据系列索引获取对应颜色
        if (seriesIndex !== undefined && options.mark.series[seriesIndex] && type === 'bar') {
          return getColorValue(options.mark.series[seriesIndex].fillPaints);
        }
      }

      return getColorValue(fillPaints);
    }
  },

  // 标记特效
  _shapeShadow: {
    source: 'shapeEffects'
  }
} as const;

export const creatLine = (chart: any, view: any, type: string, widget: Chart) => {
  const newSeries: any[] = [];
  if (widget.xField[0]) {
    const xField = widget.xField[0]?.fieldName || '';
    widget.yField.length > 0 &&
      widget.yField.forEach((item: any, index: number) => {
        const mark = view.line();

        // 设置编码
        mark.encode('x', xField);
        mark.encode('y', item.fieldName);
        mark.tooltip({ channel: 'y', name: item.fieldAlias });
        if (item.fieldAlias) {
          mark.encode('color', () => item.fieldAlias);
        }

        if (item.fieldName) {
          mark.encode('series', () => item.fieldName);
        }

        mark.markKey = item.fieldName;
        newSeries.push(mark);
      });
  }

  return newSeries;
};

/**
 * 折线图配置映射器
 */
export const mapLineConfig = (
  lineConfig: LineMark,
  type: string,
  options?: ChartOptionsType,
  seriesIndex?: number
): Record<string, any> => {
  const lineOptions: any = {};

  // 映射所有配置
  mapConfig(lineConfig, lineOptions, LINE_MAPPING, type, options, seriesIndex);

  return lineOptions;
};

/**
 * 折线图添加标记
 */
export const applyLineStyle = async (
  chart: any,
  view: any,
  styleConfig: any,
  index: number,
  type: string,
  mark: any,
  xField?: string,
  yField?: string
) => {
  if (!view) {
    return;
  }

  try {
    // 定义标记唯一的标识符
    const LineMarkKey = 'line_mark';
    // 先移除已存在的标记
    if (view.children) {
      view.children = view.children.filter((child: any) => child.markKey !== LineMarkKey);
    }

    if (styleConfig._shapeType === AdornType.None) return;
    if (styleConfig._shapeType === AdornType.Icon) {
      const { type, name } = styleConfig._mark.icon;

      if (!type || !name) return;
      const icon = await DocumentService.loadIconPath(type, name);
      const { extractSvgPaths } = useSvgParser();

      // view.point();
      // // // 创建新的点图元
      // const options = chart.options();
      // chart.clear();
      // const pathsArray = extractSvgPaths(icon as unknown as string);
      // registerIcon('', pathsArray);
      // chart.options(options);

      // view.children.forEach((child: any) => {
      //   if (child.type === 'line') {
      //     const point = chart.point();
      //     point.encode('x', child.value.encode.x).encode('y', child.value.encode.y).encode('shape', 'ddd');
      //     point.markKey = LineMarkKey;
      //   }
      // });
      // 为图元添加唯一标识符
    } else {
      // 说明是图片
      const { type, url, width } = styleConfig._mark.image;
      const { visible, fillPaints, blur, offset } = styleConfig._shapeShadow;
      const color = getColorValue(fillPaints);
      if (!url || !width) return;
      view.children.forEach((child: any) => {
        if (child.type === 'line') {
          const image = view.image();
          image.encode('x', child.value.encode.x).encode('y', child.value.encode.y);

          const imageUrl = type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;

          image
            .encode('src', imageUrl)
            .style('width', width)
            .style('height', width)
            .attr('zIndex', 0)
            .tooltip(false)
            .style('shadowColor', visible ? color : 'transparent')
            .style('shadowBlur', blur)
            .style('shadowOffsetX', offset.x)
            .style('shadowOffsetY', offset.y);

          image.markKey = LineMarkKey;
        }
      });
    }
  } catch (error) {
    console.error('创建标记失败:', error);
  }
};

export const registerIcon = (name: string, pathsArray: string[]) => {
  if (!name) return;
  const customShape = (style: any, context: any) => {
    const { document } = context;
    return (point: any) => {
      const { x, y } = point[0];
      const g = document.createElement('g', {});
      pathsArray.forEach((d) => {
        const path = document.createElement('path', {
          style: {
            d,
            fill: 'red', // 添加明显的颜色
            transform: `translate(${x},${y})`
          }
        });

        g.appendChild(path);
      });
      return g;
    };
  };
  register(`shape.point.${name}`, customShape);
};
