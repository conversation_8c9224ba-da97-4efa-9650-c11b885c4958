import { Interpolate, type ChartOptionsType, type LineMark } from '../../../models';
import { mapConfig } from './option-utils';

/**
 * 折线图配置映射表
 * <AUTHOR>
 */
const LINE_MAPPING = {
  // 折线类型配置
  shape: {
    source: 'interpolate',
    transform: (interpolate: Interpolate) => {
      if (interpolate === Interpolate.Line) {
        return 'line';
      } else if (interpolate === Interpolate.Curve) {
        return 'smooth';
      } else if (interpolate === Interpolate.Step) {
        return 'vh';
      }
    }
  }
} as const;

/**
 * 折线图配置映射器
 */
export const mapLineConfig = (lineConfig: LineMark, type: string, options?: ChartOptionsType): Record<string, any> => {
  const lineOptions: any = {};

  // 映射所有配置
  mapConfig(lineConfig, lineOptions, LINE_MAPPING, type, options);

  return lineOptions;
};
