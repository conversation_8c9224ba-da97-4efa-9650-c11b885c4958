import { Aggregator, AxisField, ChartType, ControlType, MapperType, MapperTypeName, type WidgetType } from '../models';

/**
 * 组件配置数据
 * <AUTHOR>
 */
export class WidgetConfigDataFactory {
  /**
   * 段落
   */
  get [ControlType.Paragraph]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        columns: {
          name: '内容',
          field: []
        }
      }
    };
  }

  /**
   * 选项卡
   */
  get [ControlType.TabGroup]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '标签',
          required: true,
          field: [new AxisField('c9', '类别', undefined, 'dim', 'string')]
        },
        rows: {
          name: '值',
          required: true,
          field: [new AxisField('c13', '数量', Aggregator.SUM, 'measure', 'number')]
        }
      }
    };
  }

  /**
   * 文本输入
   */
  get [ControlType.InputBox]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '文本输入',
          field: []
        }
      }
    };
  }

  /**
   * 数值输入
   */
  get [ControlType.NumberBox]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '数值输入',
          field: []
        }
      }
    };
  }

  /**
   * 滑块
   */
  get [ControlType.Slider]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '滑块值',
          field: []
        }
      }
    };
  }

  /**
   * 滑块
   */
  get [ControlType.Range]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '区间滑块',
          field: []
        }
      }
    };
  }
  get [ControlType.SelectPicker]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '标签',
          maxLen: 1,
          required: true,
          field: [new AxisField('c9', '类别', undefined, 'dim', 'string')]
        },
        rows: {
          name: '值',
          field: [new AxisField('c13', '数量', Aggregator.SUM, 'measure', 'number')]
        }
      }
    };
  }

  /**
   * 柱状图
   */
  get [ChartType.Bar]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      seriesFieldKey: 'yField',
      matchs: {
        xField: {
          name: 'X 轴',
          maxLen: 1,
          required: true,
          field: [new AxisField('c9', '类别', undefined, 'dim', 'string')]
        },
        yField: {
          name: 'Y 轴',
          required: true,
          field: [new AxisField('c13', '数量', Aggregator.SUM, 'measure', 'number')]
        }
      }
    };
  }

  /**
   * 堆叠柱状图
   */
  get [ChartType.StackBar]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      seriesFieldKey: 'yField',
      matchs: {
        xField: {
          name: 'X 轴',
          maxLen: 1,
          required: true,
          field: [new AxisField('c9', '类别', undefined, 'dim', 'string')]
        },
        yField: {
          name: 'Y 轴',
          required: true,
          field: [new AxisField('c13', '数量', Aggregator.SUM, 'measure', 'number')]
        },
        [MapperType.Color]: {
          name: MapperTypeName[MapperType.Color],
          maxLen: 1,
          field: [new AxisField('c5', '细分', undefined, 'dim', 'string', MapperType.Color)]
        }
      }
    };
  }

  /**
   * 折线图
   */
  get [ChartType.Line]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      seriesFieldKey: 'yField',
      matchs: {
        xField: {
          name: 'X 轴',
          maxLen: 1,
          required: true,
          field: [new AxisField('c9', '类别', undefined, 'dim', 'string')]
        },
        yField: {
          name: 'Y 轴',
          required: true,
          field: [new AxisField('c13', '数量', Aggregator.SUM, 'measure', 'number')]
        }
      }
    };
  }

  /**
   * 获取组件配置
   * @param widgetType 组件类型
   * @returns 组件配置
   */
  get(widgetType: WidgetType) {
    return (this as any)[widgetType];
  }
}
