import type { Records } from '@hetu/util';
import {
  BlockGroup,
  ChartGroup,
  ChartType,
  ControlGroup,
  ControlType,
  WidgetConfig,
  WidgetGroupConfig,
  type WidgetGroup,
  type WidgetType
} from '../models';

/**
 * 组件配置工厂类
 * 用于管理和创建各种组件的配置信息
 * <AUTHOR>
 */
export class WidgetConfigFactory {
  private _groups: Records<WidgetGroupConfig> = {};
  private _configs: Records<WidgetConfig> = {};

  constructor() {
    this._groups = this.createGroups();
    this._configs = this.createConfigs();
    this.initWidgetGroup();
  }

  /**
   * 获取所有组件配置
   */
  public get configs(): Records<WidgetConfig> {
    return this._configs;
  }

  /**
   * 获取指定组件的配置
   * @param type 组件名称
   */
  public getConfig(type: WidgetType): WidgetConfig | undefined {
    return this._configs[type];
  }

  /**
   * 创建组件配置
   * @private
   */
  private createConfigs(): Records<WidgetConfig> {
    return Object.assign(
      {},
      /** 组件 */
      // 信息类
      new WidgetConfig(ControlType.Paragraph, '段落', BlockGroup.Control, ControlGroup.Info, 180, 100).record,
      new WidgetConfig(ControlType.Indicator, '指标卡', BlockGroup.Control, ControlGroup.Info).record,
      new WidgetConfig(ControlType.Progress, '进度条', BlockGroup.Control, ControlGroup.Info).record,
      new WidgetConfig(ControlType.Icon, '图标', BlockGroup.Control, ControlGroup.Info).record,
      new WidgetConfig(ControlType.Image, '图片', BlockGroup.Control, ControlGroup.Info).record,
      new WidgetConfig(ControlType.Video, '视频', BlockGroup.Control, ControlGroup.Info).record,
      new WidgetConfig(ControlType.Iframe, '网页', BlockGroup.Control, ControlGroup.Info).record,
      // 交互类
      new WidgetConfig(ControlType.Button, '按钮', BlockGroup.Control, ControlGroup.Interactive).record,
      new WidgetConfig(ControlType.TabGroup, '选项卡', BlockGroup.Control, ControlGroup.Interactive, 400, 50).record,
      new WidgetConfig(ControlType.Tabs, 'Tab页签', BlockGroup.Control, ControlGroup.Interactive).record,
      new WidgetConfig(ControlType.Calendar, '日历', BlockGroup.Control, ControlGroup.Interactive).record,
      // 表单类
      new WidgetConfig(ControlType.InputBox, '文本输入', BlockGroup.Control, ControlGroup.Form, 200, 32).record,
      new WidgetConfig(ControlType.NumberBox, '数值输入', BlockGroup.Control, ControlGroup.Form, 200, 32).record,
      new WidgetConfig(ControlType.Slider, '数值滑块', BlockGroup.Control, ControlGroup.Form, 200, 32).record,
      new WidgetConfig(ControlType.Range, '区间滑块', BlockGroup.Control, ControlGroup.Form, 200, 32).record,
      new WidgetConfig(ControlType.SelectPicker, '下拉框', BlockGroup.Control, ControlGroup.Form, 200, 32).record,
      new WidgetConfig(ControlType.TreePicker, '树形下拉', BlockGroup.Control, ControlGroup.Form).record,
      new WidgetConfig(ControlType.CascaderPicker, '级联下拉', BlockGroup.Control, ControlGroup.Form).record,
      new WidgetConfig(ControlType.DatePicker, '日期', BlockGroup.Control, ControlGroup.Form).record,
      new WidgetConfig(ControlType.DatetimePicker, '日期时间', BlockGroup.Control, ControlGroup.Form).record,
      new WidgetConfig(ControlType.TimePicker, '时间', BlockGroup.Control, ControlGroup.Form).record,
      // 表格类
      new WidgetConfig(ControlType.PivotSheet, '交叉表', BlockGroup.Control, ControlGroup.Sheet).record,
      new WidgetConfig(ControlType.Sheet, '明细表', BlockGroup.Control, ControlGroup.Sheet).record,
      new WidgetConfig(ControlType.TreeSheet, '树形表格', BlockGroup.Control, ControlGroup.Sheet).record,
      new WidgetConfig(ControlType.RollSheet, '轮播表格', BlockGroup.Control, ControlGroup.Sheet).record,
      // 导航类
      new WidgetConfig(ControlType.NavMenu, '导航菜单', BlockGroup.Control, ControlGroup.Nav).record,
      new WidgetConfig(ControlType.Steps, '步骤条', BlockGroup.Control, ControlGroup.Nav).record,
      new WidgetConfig(ControlType.Pagination, '分页', BlockGroup.Control, ControlGroup.Nav).record,
      new WidgetConfig(ControlType.Breadcrumb, '面包屑', BlockGroup.Control, ControlGroup.Nav).record,

      /** 图表 */
      // 饼环图
      new WidgetConfig(ChartType.Pie, '饼图', BlockGroup.Chart, ChartGroup.Pie).record,
      new WidgetConfig(ChartType.CirclePie, '环形饼图', BlockGroup.Chart, ChartGroup.Pie).record,
      new WidgetConfig(ChartType.RosePie, '玫瑰饼图', BlockGroup.Chart, ChartGroup.Pie).record,
      new WidgetConfig(ChartType.CarouselPie, '轮播饼图', BlockGroup.Chart, ChartGroup.Pie).record,
      new WidgetConfig(ChartType.Radial, '对比环图', BlockGroup.Chart, ChartGroup.Pie).record,
      // 柱状/条形图
      new WidgetConfig(ChartType.Bar, '柱状图', BlockGroup.Chart, ChartGroup.BarStrip, 300, 200).record,
      new WidgetConfig(ChartType.StackBar, '堆叠柱状图', BlockGroup.Chart, ChartGroup.BarStrip, 400, 300).record,
      new WidgetConfig(ChartType.PercentageBar, '百分比柱状图', BlockGroup.Chart, ChartGroup.BarStrip, 300, 200).record,
      new WidgetConfig(ChartType.Composed, '组合图', BlockGroup.Chart, ChartGroup.BarStrip).record,
      new WidgetConfig(ChartType.BulletBar, '子弹图', BlockGroup.Chart, ChartGroup.BarStrip).record,
      new WidgetConfig(ChartType.Strip, '条形图', BlockGroup.Chart, ChartGroup.BarStrip).record,
      new WidgetConfig(ChartType.StackStrip, '堆叠条形图', BlockGroup.Chart, ChartGroup.BarStrip).record,
      new WidgetConfig(ChartType.PercentageStrip, '百分比条形图', BlockGroup.Chart, ChartGroup.BarStrip).record,
      // 折线图
      new WidgetConfig(ChartType.Line, '折线图', BlockGroup.Chart, ChartGroup.Line).record,
      new WidgetConfig(ChartType.area, '区域图', BlockGroup.Chart, ChartGroup.Line).record,
      new WidgetConfig(ChartType.PercentageArea, '区域百分比图', BlockGroup.Chart, ChartGroup.Line).record,
      // 散点图
      new WidgetConfig(ChartType.Scatter, '散点图', BlockGroup.Chart, ChartGroup.Scatter).record,
      new WidgetConfig(ChartType.BubbleScatter, '气泡图', BlockGroup.Chart, ChartGroup.Scatter).record,
      // 漏斗图
      new WidgetConfig(ChartType.Funnel, '漏斗图', BlockGroup.Chart, ChartGroup.Funnel).record,
      // 雷达图
      new WidgetConfig(ChartType.Radar, '雷达图', BlockGroup.Chart, ChartGroup.Radar).record,
      // 仪表盘
      new WidgetConfig(ChartType.Gauge, '仪表盘', BlockGroup.Chart, ChartGroup.Gauge).record,
      // 其他
      new WidgetConfig(ChartType.Liquidfill, '水波图', BlockGroup.Chart, ChartGroup.Other).record,
      new WidgetConfig(ChartType.WordCloud, '词云', BlockGroup.Chart, ChartGroup.Other).record
    );
  }

  /**
   * 获取所有组件分组配置
   */
  public get groups(): Records<WidgetGroupConfig> {
    return this._groups;
  }

  /**
   * 获取指定组件分组配置
   * @param type 组件分组类型
   */
  public getGroup(type: WidgetGroup): WidgetGroupConfig | undefined {
    return this._groups[type];
  }

  /**
   * 创建组件分组配置
   * @private
   */
  private createGroups(): Records<WidgetGroupConfig> {
    return Object.assign(
      {},
      /** 图表分组 */
      new WidgetGroupConfig(ChartGroup.Pie, '饼环图', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.BarStrip, '柱状/条形图', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.Line, '折线图', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.Scatter, '散点图', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.Funnel, '漏斗图', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.Radar, '雷达图', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.Gauge, '仪表盘', BlockGroup.Chart).record,
      new WidgetGroupConfig(ChartGroup.Other, '其他', BlockGroup.Chart).record,
      /** 控件分组 */
      new WidgetGroupConfig(ControlGroup.Info, '信息类', BlockGroup.Control).record,
      new WidgetGroupConfig(ControlGroup.Sheet, '表格类', BlockGroup.Control).record,
      new WidgetGroupConfig(ControlGroup.Form, '表单类', BlockGroup.Control).record,
      new WidgetGroupConfig(ControlGroup.Nav, '导航类', BlockGroup.Control).record,
      new WidgetGroupConfig(ControlGroup.Interactive, '交互类', BlockGroup.Control).record
    );
  }

  /**
   * 初始化组件分组
   * @private
   */
  private initWidgetGroup() {
    Object.values(this._configs).forEach((config) => {
      this._groups[config.subGroup].list.push(config);
    });
  }
}
