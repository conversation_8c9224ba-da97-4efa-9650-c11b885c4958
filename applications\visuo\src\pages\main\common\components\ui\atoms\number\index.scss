@import '../../../../../styles/index.scss';

.vis-number {
  &-container {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
  }

  &-suffix {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    transition: left 0.1s ease;
    font-size: $primary-font-size;
    pointer-events: none;
    user-select: none;
  }

  &:focus-within .vis-number-suffix {
    opacity: 0.7;
  }
}
