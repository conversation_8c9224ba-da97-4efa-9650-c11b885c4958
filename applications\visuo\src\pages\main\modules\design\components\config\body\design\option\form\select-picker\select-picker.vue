<template>
  <div class="vis-config-select-picker-option">
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 基础 </span>
      </div>
      <div class="vis-config-card__body">
        <!-- 默认值 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">默认值</div>
              <div class="vis-form-field__content w-full">
                <vis-select
                  class="w-full overflow-hidden flex-basis-46"
                  v-model="computedOptions.defaultValue"
                  :options="pickerOptions"
                  clearable
                  :multiple="isMultiple"
                >
                  <template v-if="noSelected" #selected>{{ computedOptions.placeholder }}</template>
                  <template #no-option>
                    <q-item>列表为空</q-item>
                  </template>
                </vis-select>
              </div>
            </div>
          </div>
        </div>
        <!-- 模式 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">模式</div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="computedOptions.mode" :options="modeOptions" />
              </div>
            </div>
            <div class="vis-form-field">
              <div class="vis-form-field__label">显示数量</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.showNumber" :disabled="!isMultiple" :min="1" />
              </div>
            </div>
          </div>
        </div>

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 占位符 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">占位符</div>
              <div class="vis-form-field__content">
                <q-input
                  v-model="computedOptions.placeholder"
                  placeholder="请输入占位符"
                  flat
                  borderless
                  class="vis-input w-full rounded-borders px-2"
                />
              </div>
            </div>
            <!-- 搜索 -->
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-checkbox v-model="computedOptions.filterable" label="允许搜索" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <q-separator />

    <!-- 样式设置 -->
    <vis-form-box-style :options="computedOptions" title="框样式" />
    <q-separator />

    <!-- 选项标签 -->
    <vis-form-option :options="options.optionStyle" />
    <q-separator />

    <!-- 标签设置 -->
    <vis-form-label :options="options.label" @handleManage="labelManage" />
    <q-separator />

    <!-- 前后缀 -->
    <vis-form-fix :prefix="computedOptions.prefix" :suffix="computedOptions.suffix"></vis-form-fix>
    <q-separator />

    <!-- 校验规则 -->
    <vis-form-rules :options="computedOptions.rules" :ruleList="ruleList"></vis-form-rules>
  </div>
</template>
<script lang="ts" src="./select-picker.ts"></script>
