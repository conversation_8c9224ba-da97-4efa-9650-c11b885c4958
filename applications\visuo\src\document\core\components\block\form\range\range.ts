import { computed, defineComponent, nextTick, onMounted, ref, watch, type PropType } from 'vue';
import { AroundPosition, Range } from '../../../../models';
import { useBase, useFill, useFix, useUiStyle } from '../../../../hooks';
import type { Records } from '@hetu/util';
import { isNumber } from 'lodash-es';
/**
 * 区间滑块
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-range',
  props: {
    widget: {
      type: Object as PropType<Range>,
      required: true
    },
    block: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const { getLabelStyle, getStrokeStyle, getEffectStyle } = useUiStyle();
    const { getFillStyle } = useFill();
    const { getFixAndStyle } = useFix();

    const computedOptions = computed(() => {
      return props.widget.options;
    });

    // 标签显示位置在上
    const topPosition = computed(() => {
      return computedOptions.value.label?.position === 'top';
    });
    // 标签样式
    const labelStyle = computed(() => getLabelStyle(computedOptions.value.label));

    const dataValue = ref<(number | null)[]>([null, null]);
    const inputValue = ref({
      min: computedOptions.value.defaultValue[0],
      max: computedOptions.value.defaultValue[1]
    });
    const labelValue = computed(() => {
      return {
        min: `${inputValue.value.min}${computedOptions.value.thumbLabel?.suffix}`,
        max: `${inputValue.value.max}${computedOptions.value.thumbLabel?.suffix}`
      };
    });
    const labelInline = computed(() => {
      return computedOptions.value.thumbLabel?.position === AroundPosition.Right;
    });

    watch(
      [() => dataValue.value, () => computedOptions.value.defaultValue],
      (val) => {
        inputValue.value.min = isNumber(val[0][0]) ? val[0][0] : val[1][0];
        inputValue.value.max = isNumber(val[0][1]) ? val[0][1] : val[1][1];
      },
      { deep: true }
    );

    const onValueChange = () => {
      emit('change', [inputValue.value.min, inputValue.value.max]);
    };

    // 滑块样式
    const style = computed(() => {
      const style: Records = {};
      if (computedOptions.value) {
        const { trackHeight, trackFillPaints, fillPaints, thumb, thumbLabel, fontStyle } = computedOptions.value;
        // 轨道样式
        if (trackHeight) {
          style['--track-height'] = `${trackHeight}px`;
        }
        if (trackFillPaints) {
          const tmp = getFillStyle(trackFillPaints);
          style['--selection-background'] =
            tmp.backgroundColor ||
            `${tmp.backgroundImage} ${tmp.backgroundPosition} / ${tmp.backgroundSize} ${tmp.backgroundRepeat}`;
        }
        if (fillPaints) {
          const tmp = getFillStyle(fillPaints);
          style['--track-background'] =
            tmp.backgroundColor ||
            `${tmp.backgroundImage} ${tmp.backgroundPosition} / ${tmp.backgroundSize} ${tmp.backgroundRepeat}`;
        }
        // 按钮样式
        if (thumb) {
          const { fillPaints, stroke, effects } = thumb;
          if (fillPaints) {
            const tmp = getFillStyle(fillPaints);
            style['--thumb-background'] =
              tmp.backgroundColor ||
              `${tmp.backgroundImage} ${tmp.backgroundPosition} / ${tmp.backgroundSize} ${tmp.backgroundRepeat}`;
          }

          if (stroke) {
            const tmp = getStrokeStyle(stroke);
            style['--thumb-border-style'] = tmp.borderStyle;
            style['--thumb-border-width'] = tmp.borderWidth;
            if (tmp.borderImage === 'initial') {
              style['--thumb-border-color'] = tmp.borderColor;
              style['--thumb-border-radius'] = '50%';
            } else {
              style['--thumb-border-image'] = tmp.borderImage;
              style['--thumb-border-image-slice'] = tmp.borderImageSlice;
            }
          }

          if (effects) {
            const tmp = getEffectStyle(effects);
            style['--thumb-box-shadow'] = tmp.boxShadow;
          }
        }
        // 按钮标签样式
        if (thumbLabel) {
          const { textFillPaints, fillPaints } = thumbLabel;
          if (textFillPaints) {
            const tmp = getFillStyle(textFillPaints);
            style['--thumb-label-color'] =
              tmp.backgroundColor ||
              `${tmp.backgroundImage} ${tmp.backgroundPosition} / ${tmp.backgroundSize} ${tmp.backgroundRepeat}`;
          }
          if (fillPaints) {
            const tmp = getFillStyle(fillPaints);
            style['--thumb-label-background'] =
              tmp.backgroundColor ||
              `${tmp.backgroundImage} ${tmp.backgroundPosition} / ${tmp.backgroundSize} ${tmp.backgroundRepeat}`;
          }
        }

        if (fontStyle.fontSize) {
          style['--thumb-label-font-size'] = `${fontStyle.fontSize}px`;
        }
      }
      return style;
    });

    // 前后缀
    const fixAndStyle = computed(() => getFixAndStyle(computedOptions.value));

    // #region 数据加载
    const { getWidgetFieldData, loadStaticData } = useBase();
    const loadWidgetData = async () => {
      return new Promise((resolve) => {
        if (props.widget.datasetType === 'static') {
          loadStaticData().then((res: any) => {
            if (res) {
              const [key1, key2] = Object.keys(res[0]);
              dataValue.value[0] = key1 ? res[0][key1] : null;
              dataValue.value[1] = key2 ? res[0][key2] : null;
            }
            resolve(res);
          });
          return;
        }
        getWidgetFieldData(
          new Promise((resolve) => {
            setTimeout(() => {
              console.log('InputComponent: 重写加载区间滑块数据', props.widget.id);
              resolve(true);
            }, 5000);
          })
        ).then((res) => {
          resolve(res);
        });
      });
    };
    // #endregion

    // #region 外部方法
    const checkValue = (val: number | null) => {
      let ret = val;
      if (isNumber(ret)) {
        ret = Math.max(ret, computedOptions.value.min);
        ret = Math.min(ret, computedOptions.value.max);
      }
      return ret;
    };
    const setValue = (val: (number | null)[]) => {
      inputValue.value.min = checkValue(val[0]) || computedOptions.value.min;
      inputValue.value.max = checkValue(val[1]) || computedOptions.value.min;
    };
    // #endregion

    // #region 标签位置
    const contentRef = ref();
    onMounted(() => {
      watch(
        [
          () => props.block.height,
          () => computedOptions.value.fontStyle.fontSize,
          () => computedOptions.value.trackHeight,
          () => computedOptions.value.thumbLabel.position
        ],
        (val) => {
          nextTick(() => {
            const labelEle = contentRef.value.querySelector('.thumb-label__top');
            if (!labelEle) return;
            const labelHeight = computedOptions.value.fontStyle.fontSize * 1.5;

            const minus = val[0] / 2 - labelHeight - computedOptions.value.trackHeight;
            if (minus <= 0) {
              (labelEle as HTMLElement).style.top = `${minus}px`;
            } else {
              (labelEle as HTMLElement).style.top = '0px';
            }
          });
        },
        { immediate: true }
      );
    });
    // #endregion

    return {
      computedOptions,
      topPosition,
      labelStyle,
      labelInline,

      inputValue,
      labelValue,
      onValueChange,
      style,

      loadWidgetData,
      fixAndStyle,
      setValue,

      contentRef
    };
  }
});
