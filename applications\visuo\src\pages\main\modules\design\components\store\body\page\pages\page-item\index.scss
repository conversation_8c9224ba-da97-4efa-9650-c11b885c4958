@import '../../../../../index';

.#{$vis-prefix}-store-page {
  @apply p-0;

  &-node,
  &-node__parent {
    @apply p-0;
  }

  &-node__parent,
  &-node__child {
    @apply mt-2;
  }

  &-node.drag-target-parent {
    background-color: $tree-item-hover !important;
  }

  &-node__parent {
    &.selected {
      background-color: $tree-item-selected-lighter;

      > .#{$vis-prefix}-store-page-header {
        background-color: $tree-item-selected;
      }

      div.#{$vis-prefix}-store-page-header:hover,
      div.#{$vis-prefix}-store-page-node__child:hover {
        // background-color: transparent;
        background-color: $tree-item-selected-hover;
      }

      > div.#{$vis-prefix}-store-page-header:hover {
        background-color: $tree-item-selected;
      }
    }

    &:not(.selected) {
      > .#{$vis-prefix}-store-page-header:hover {
        background-color: $tree-item-hover;
      }
    }
  }

  &-node__child {
    // @apply pl-12px;

    &:not(.selected):hover {
      @apply rounded;

      background-color: $tree-item-hover;
    }

    &.selected {
      background-color: $tree-item-selected;
    }

    &.active {
      @apply font-500;

      background-color: $tree-item-hover;
    }
  }

  &-node.drag-target-parent,
  &-node__parent,
  &-node__child,
  .#{$vis-prefix}-store-page-header {
    @apply rounded;
  }

  &-header {
    @apply flex flex-nowrap items-center cursor-pointer;

    &-expand {
      @apply flex justify-between;

      &-arrow {
        @apply flex items-center transition-transform transition-duration-300 ease;

        font-size: $primary-font-size;
      }

      &-arrow.rotate {
        @apply rotate-90;
      }
    }

    &-content {
      @apply flex justify-between w-full;

      &-title {
        @apply w-full flex items-center gap-1 lh-6 h-6;

        font-size: $primary-font-size;

        &-icon {
          @apply flex items-center ml-1;

          font-size: $icon-font-size;
        }

        &-text {
          @apply w-full h-full;

          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;

          // position: relative;
          // line-height: 1.75rem;
          // max-height: 1.75rem; /* 1行的高度 */
          // overflow: hidden;

          // &::after {
          //   content: '...';
          //   position: absolute;
          //   bottom: 0;
          //   right: 0;
          // }
        }
      }

      &-side {
        @apply flex items-center;

        &-icon {
          @apply flex items-center mr-5px;

          font-size: $icon-font-size;
        }
      }
    }
  }

  &-collapsible {
    @apply grid transition-property-all transition-duration-300 ease overflow-hidden;

    grid-template-rows: 1fr;
  }

  &-collapsible.folder {
    grid-template-rows: 0fr;
  }

  &-children {
    // pl-6px
    @apply min-h-0px;
  }

  &-ghost {
    @apply h-0 overflow-hidden border-b-1px border-b-solid border-b-[#000];
  }

  &-drag {
    @apply h-6 overflow-hidden;
  }
}
