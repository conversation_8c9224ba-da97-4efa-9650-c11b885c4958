import { isNumber } from 'lodash-es';
import {
  ChartType,
  type BarMark,
  type BarOptions,
  Chart,
  type ChartOptionsType,
  type FillPaints
} from '../../../models';
import { getColorValue, getLineDashFromStroke, mapConfig } from './option-utils';

/**
 * 柱状图配置映射表
 * <AUTHOR>
 */
const BAR_MAPPING = {
  // 填充样式
  fill: {
    source: 'fillPaints',
    transform: (fillPaints: FillPaints, config: any, type: string, options: any, seriesIndex?: number) => {
      // 单系列情况：使用全局 fillPaints
      if (options.mark.series.length === 1 && type === 'bar') {
        return getColorValue(options.mark.fillPaints);
      }

      // 多系列情况：根据系列索引获取对应颜色
      if (seriesIndex !== undefined && options.mark.series[seriesIndex] && type === 'bar') {
        return getColorValue(options.mark.series[seriesIndex].fillPaints);
      }

      return (d: any, index: any, data: any, column: any) => {
        const keys = Object.keys(d).map((key) => key + '-' + d[key]);
        const series = options.mark.series.find((item: any) => keys.includes(item.id));
        return getColorValue(series?.fillPaints);
      };
    }
  },

  // 圆角配置
  radiusTopLeft: { source: 'radius.0', default: 0 },
  radiusTopRight: { source: 'radius.1', default: 0 },
  radiusBottomRight: { source: 'radius.2', default: 0 },
  radiusBottomLeft: { source: 'radius.3', default: 0 },

  // 柱子尺寸配置
  minHeight: { source: 'height', default: 20 },
  minWidth: {
    source: 'width'
  },
  maxWidth: {
    source: 'width'
  },
  columnWidthRatio: {
    source: '',
    transform: () => 1
  },
  _paddingInner: {
    source: 'groupGap',
    default: 0,
    transform: (gap: number, config: BarMark, type: string, options: BarOptions) => {
      const padding = gap > 0 ? Math.min(gap / 100, 1) : 0;
      return padding;
    }
  },
  _padding: {
    source: 'gap',
    default: 0,
    transform: (gap: number, config: BarMark, type: string, options: BarOptions) => {
      const padding = gap > 0 ? Math.min(gap / 100, 1) : 0;
      return padding;
    }
  },

  // 背景配置 - 通过特殊属性标记需要创建背景图元
  _needBackground: {
    source: 'background.fillPaints.visible',
    transform: (visible: boolean) => visible || false
  },
  _backgroundStyle: {
    source: 'background',
    transform: (background: any, config: BarMark, type: string, options: BarOptions) => {
      // 计算背景宽度，考虑系列数量
      const seriesCount = options.mark?.series?.length || 1;
      const baseWidth = config.width || 20;

      return {
        fill: getColorValue(background.fillPaints),
        fillOpacity: background.fillPaints?.color?.a || 0,
        width: baseWidth,
        stroke: getColorValue(background.stroke?.fillPaints),
        strokeDash: getLineDashFromStroke(background.stroke),
        strokeWidth: background.stroke?.position?.[0] || 0,
        strokeOpacity: background.stroke?.fillPaints?.color?.a || 0,
        radiusTopLeft: config.radius[0] || 0,
        radiusTopRight: config.radius[1] || 0,
        radiusBottomRight: config.radius[2] || 0,
        radiusBottomLeft: config.radius[3] || 0
      };
    }
  },

  // 分组和堆叠配置
  _showType: { source: 'showType', default: 'stack' },
  preBar: {
    source: 'preBar',
    default: true
  }
} as const;

/**
 * 创建柱状图
 */
export const creatBar = (chart: any, view: any, type: string, widget: Chart) => {
  const newSeries: any[] = [];
  if (widget.xField[0]) {
    const xField = widget.xField[0]?.fieldName || '';
    widget.yField.length > 0 &&
      widget.yField.forEach((item: any, index: number) => {
        const mark = view.interval();

        // 设置编码
        mark.encode('x', xField);
        mark.encode('y', item.fieldName);
        mark.tooltip({ channel: 'y', name: item.fieldAlias });
        if (item.fieldAlias) {
          mark.encode('color', () => item.fieldAlias);
        }

        if (item.fieldName) {
          mark.encode('series', () => item.fieldName);
        }

        mark.markKey = item.fieldName;
        newSeries.push(mark);
      });
  }

  return newSeries;
};

/**
 * 创建柱状图
 */
export const creatStackBar = (chart: any, view: any, type: string, widget: Chart) => {
  const newSeries: any[] = [];
  const yField = widget.yField[0];
  if (widget.xField[0] && yField) {
    const xFieldName = widget.xField[0]?.fieldName || '';

    const colorField = widget.dataMapping[0];
    const mark = view.interval();

    // 设置编码
    mark.encode('x', xFieldName);
    mark.encode('y', yField?.fieldName);

    if (colorField?.fieldName) {
      mark.encode('color', colorField.fieldName);
      mark.tooltip(false);
    }

    mark.markKey = yField?.fieldName;
    view.value.transform = [];
    view.transform({ type: 'stackY' });
    //view.transform({ type: 'dodgeX' })
    newSeries.push(mark);
  }

  return newSeries;
};

/**
 * 柱状图配置映射器
 */
export const mapBarConfig = (
  barConfig: BarMark,
  type: string,
  options?: ChartOptionsType,
  seriesIndex?: number
): Record<string, any> => {
  const barOptions: any = {};
  // 映射所有配置
  mapConfig(barConfig, barOptions, BAR_MAPPING, type, options, seriesIndex);

  return barOptions;
};

/**
 * 应用图形图样式配置
 */
export const applyBarStyle = async (
  chart: any,
  view: any,
  styleConfig: any,
  index: number,
  type: ChartType,
  mark: any,
  xField?: string,
  yField?: string
) => {
  if (!mark || !styleConfig) {
    return;
  }
  if (styleConfig && styleConfig._backgroundStyle) {
    setTimeout(async () => {
      await addBarBackground(chart, view, styleConfig, index, xField, yField);
    }, 300);

    chart.off('afterchangesize');
    chart.on('afterchangesize', async () => {
      // 延迟添加背景，确保图表完全渲染
      setTimeout(async () => {
        await updateBarBackground(chart, view, styleConfig);
      }, 300);
    });
  }

  if (isNumber(styleConfig._paddingInner)) {
    view.scale('x', {
      paddingInner: styleConfig._paddingInner
    });
  }
  if (isNumber(styleConfig._padding)) {
    const intervalMarks =
      view.children?.filter((child: any) => child.type === 'interval' && child.markKey !== 'bar_background_mark') || [];
    const seriesCount = intervalMarks.length;

    if (seriesCount > 1) {
      const barWidth = styleConfig.maxWidth || styleConfig.minWidth || 20;
      const baseOffset = barWidth * styleConfig._padding;

      const relativePosition = index - (seriesCount - 1) / 2;
      const offset = relativePosition * baseOffset;

      if (offset < 0) {
        // 向左偏移
        mark.style('insetLeft', 0);
        mark.style('insetRight', Math.abs(offset));
      } else if (offset > 0) {
        // 向右偏移
        mark.style('insetLeft', offset);
        mark.style('insetRight', 0);
      } else {
        // 居中（奇数个系列的中间系列）
        mark.style('insetLeft', 0);
        mark.style('insetRight', 0);
      }
    }
  }

  if ([ChartType.StackBar].includes(type)) {
    view.value.transform = [];

    switch (styleConfig._showType) {
      case 'stack':
        view.transform({ type: 'stackY' });
        break;
      case 'group':
        isNumber(styleConfig._padding)
          ? view.transform({ type: 'dodgeX', padding: styleConfig._padding })
          : view.transform({ type: 'dodgeX' });
        setTimeout(async () => {
          await updateBarBackground(chart, view, styleConfig);
        }, 200);
        break;
      case 'percent-stack':
        view
          .transform({ type: 'stackY' })
          .transform({ type: 'normalizeY' })
          .scale('y', { domain: undefined })
          .axis('y', { ...view.axis().y, labelFormatter: '.0%' });
        break;
    }
  }
};

/**
 * 为柱状图添加背景图元
 */
export const addBarBackground = async (
  chart: any,
  view: any,
  styleConfig: any,
  index: number,
  xField?: string,
  yField?: string
) => {
  const backgroundStyle = styleConfig._backgroundStyle;

  if (!view || !backgroundStyle) {
    return;
  }

  try {
    // 定义背景图元的唯一标识符
    const backgroundMarkKey = 'bar_background_mark';

    // 先移除已存在的背景图元
    if (view.children) {
      view.children = view.children.filter((child: any) => child.markKey !== backgroundMarkKey);
    }
    if (!styleConfig._needBackground || !chart.getView()) {
      return;
    }

    // 使用传入的字段名，如果没有传入则尝试从数据推断
    let finalXField = xField;
    let finalYField = yField;

    if (!finalXField || !finalYField) {
      const viewData = view?.value?.data || view?.data || [];
      if (!viewData.length) {
        return;
      }
      const sampleData = viewData[0];
      const fieldNames = Object.keys(sampleData);
      finalXField = finalXField || fieldNames[0];
      finalYField = finalYField || fieldNames[index + 1];
    }

    // 使用抽取的方法获取正确的宽度和高度
    const backgroundWidth = await getBackgroundWidth(chart, view, backgroundStyle, styleConfig._showType);
    const backgroundHeight = await getBackgroundHeight(chart, backgroundStyle);

    const yMin = chart.getScaleByChannel('y')?.options.domainMin;
    const yMax = chart.getScaleByChannel('y')?.options.domainMax;
    const backgroundMark = view.interval();

    // 创建新的背景图元
    backgroundMark
      .encode('x', finalXField)
      .encode('y', yMin ? () => yMin : finalYField)
      .style('fill', backgroundStyle.fill || '')
      .style('stroke', backgroundStyle.stroke || '')
      .style('lineWidth', backgroundStyle.strokeWidth || 0)
      .style('lineDash', backgroundStyle.strokeDash || 0)
      .style('radiusTopLeft', backgroundStyle.radiusTopLeft || 0)
      .style('radiusTopRight', backgroundStyle.radiusTopRight || 0)
      .style('radiusBottomRight', backgroundStyle.radiusBottomRight || 0)
      .style('radiusBottomLeft', backgroundStyle.radiusBottomLeft || 0)
      .style('strokeOpacity', backgroundStyle.strokeOpacity || 0)
      .style('minHeight', backgroundHeight)
      .style('minWidth', backgroundWidth)
      .style('maxWidth', backgroundWidth)
      /* .style('shadowColor', backgroundStyle.fill || '')
      .style('shadowBlur', 0.1)
      .style('shadowOffsetX', 0)
      .style('shadowOffsetY', backgroundHeight) */
      .style('fillOpacity', backgroundStyle.fillOpacity || 0)
      .style('opacity', styleConfig._needBackground ? 1 : 0)
      .style('animate', false)
      .style('animate', false)
      .tooltip(false)
      /* .scale('y', {
        zero: false,
        nice: false,
        clamp: true
      }) */
      .attr('zIndex', -1);

    // 为背景图元添加唯一标识符
    backgroundMark.markKey = backgroundMarkKey;

    // 强制重新渲染以确保背景正确显示
    await chart.render();
  } catch (error) {
    console.error('创建柱背景失败:', error);
  }
};

/**
 * 获取背景图元的宽度
 * @param chart 图表实例
 * @param view 视图实例
 * @param backgroundStyle 背景样式配置
 * @returns 背景宽度
 */
export const getBackgroundWidth = async (
  chart: any,
  view: any,
  backgroundStyle: any,
  _showType: string
): Promise<number> => {
  if (!chart || !view) {
    return backgroundStyle.width || 20;
  }

  try {
    const backgroundMarkKey = 'bar_background_mark';
    const intervalMarks = view.children?.filter((child: any) => child.markKey !== backgroundMarkKey) || [];
    const seriesCount = intervalMarks.length;
    if (seriesCount > 1) {
      const group = chart.getGroup();
      const marks: any[] = [];

      const traverseGroupNodes = (node: any) => {
        if (node.childNodes) {
          node.childNodes.forEach((child: any) => {
            if (child.markType === 'interval' && child.attributes.preBar) {
              marks.push(child);
            }
            traverseGroupNodes(child);
          });
        }
      };

      traverseGroupNodes(group);

      // 按照 markKey 对 intervalMarks 进行分组
      const groupMarks = marks.reduce((acc: any, mark: any) => {
        const markKey = mark.__data__.markKey || 'default';
        if (!acc[markKey]) {
          acc[markKey] = [];
        }
        acc[markKey].push(mark);
        return acc;
      }, {});

      // 获取每组中的第一个元素并比较位置
      const firstElements = Object.keys(groupMarks)
        .map((key) => groupMarks[key][0])
        .filter(Boolean);

      if (firstElements.length > 0) {
        const bboxes = firstElements.map((element) => element.getBBox());
        const leftmost = Math.min(...bboxes.map((bbox) => bbox.left));
        const rightmost = Math.max(...bboxes.map((bbox) => bbox.right));
        return rightmost - leftmost;
      }
    } else if (_showType === 'group') {
      // 获取 x 轴的比例尺
      const xScale = chart.getScale().x;

      const { innerWidth } = chart.getView()?.layout || {};
      // 获取带宽值 
      const bandWidth = xScale.getBandWidth?.() ?? 0;

      // 计算步长值（相邻类别中心点的距离）
      const domain = xScale.getOptions()?.domain || [];
      const range = xScale.getOptions()?.range || [0, 1];
      const rangeLength = range[1] - range[0];
      const paddingInner = xScale.getOptions()?.paddingInner || 0;
      const paddingOuter = xScale.getOptions()?.paddingOuter || 0;
      const step = rangeLength / (domain.length - paddingInner + paddingOuter * 2);
    /*   console.log('当前步长值:', step);
      console.log('step * paddingInner:', step * paddingInner);
      console.log('当前带宽值:'); */

      return bandWidth * innerWidth;
    }

    return backgroundStyle.width || 20;
  } catch (error) {
    console.error('获取背景宽度失败:', error);
    return backgroundStyle.width || 20;
  }
};

/**
 * 获取背景图元的高度
 * @param chart 图表实例
 * @param backgroundStyle 背景样式配置
 * @returns 背景高度
 */
export const getBackgroundHeight = async (chart: any, backgroundStyle: any): Promise<number> => {
  if (!chart) {
    return chart?._height || 400;
  }

  try {
    const { innerHeight } = chart.getView()?.layout || {};
    return innerHeight - (backgroundStyle.strokeWidth || 0) || chart._height || 400;
  } catch (error) {
    console.error('获取背景高度失败:', error);
    return chart._height || 400;
  }
};

/**
 * 柱状图修改背景图元
 */
export const updateBarBackground = async (chart: any, view: any, styleConfig: any): Promise<void> => {
  const backgroundStyle = styleConfig._backgroundStyle;

  if (!view || !backgroundStyle) {
    return;
  }

  // 定义背景图元的唯一标识符
  const backgroundMarkKey = 'bar_background_mark';

  const backgroundMark = view.children?.find((child: any) => child.markKey === backgroundMarkKey);

  if (!backgroundMark) {
    return;
  }

  // 使用抽取的方法获取正确的宽度和高度
  const backgroundWidth = await getBackgroundWidth(chart, view, backgroundStyle, styleConfig._showType);
  const backgroundHeight = await getBackgroundHeight(chart, backgroundStyle);
  // 创建新的背景图元
  backgroundMark
    .style('minHeight', backgroundHeight)
    .style('minWidth', backgroundWidth)
    .style('maxWidth', backgroundWidth);

  await chart.render();
};
