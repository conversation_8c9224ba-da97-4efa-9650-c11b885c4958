import { ref, onUnmounted, getCurrentInstance, type ComponentPublicInstance, computed } from 'vue';
import type { Axis, Block, Chart, ChartType, Legend, Mark, TotalLabel } from '../../models';
import {
  applyLegendConfig,
  mapAxisConfig,
  mapScaleConfig,
  applyAxisConfig,
  applyScaleConfig,
  createChart,
  createChartView,
  createMarks,
  renderChart,
  updateChartData,
  resizeChart,
  destroyChart,
  clearChart,
  mapLegendConfig,
  mapMarkConfig,
  applyMarkStyle,
  getChartView,
  mapMarkStyle,
  mapLabelConfig,
  applyLabelConfig,
  getChartOptions
} from './';

/**
 * 图表 Hooks
 * <AUTHOR>
 */
export const useChart = () => {
  /**
   * 组件实例
   */
  const instance = getCurrentInstance();

  /**
   * 获取组件实例代理
   */
  const component = instance?.proxy as ComponentPublicInstance & {
    $props: { widget: Chart; block: Block; widgetData: any[] };
  };

  const widget = computed(() => component.$props.widget || null);
  const block = computed(() => component.$props.block || null);
  const widgetData = computed(() => component.$props.widgetData || []);
  const options = computed(() => JSON.parse(JSON.stringify(widget.value.options)));

  const chart = ref<any>();
  const view = ref<any>();
  const series = ref<any>();

  /**
   * 清理视图
   */
  const clear = () => {
    if (chart.value) {
      clearChart(chart.value);
      view.value = undefined;
    }
  };

  /**
   * 初始化图表
   */
  const init = async (container: HTMLElement, options: any = {}) => {
    if (chart.value) {
      destroy();
    }

    chart.value = createChart(container, options);
    return chart.value;
  };

  const createSeries = (): any => {
    const newSeries = createMarks(chart.value, view.value, widget.value);

    return newSeries;
  };

  /**
   * 配置图表
   */
  const configureChart = async (data: any[] = widgetData.value, config: any = options.value) => {
    if (!chart.value || !data) {
      return;
    }

    // 清理旧视图
    clear();
    // 创建视图

    view.value = createChartView(chart.value, data);

    series.value = createSeries();
    updateChartConfig(config, true);
  };

  /**
   * 更新X轴配置
   */
  const updateXAxisConfig = (xAxisConfig: Axis = <Axis>options.value?.xAxis, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    // 映射X轴配置
    const g2Config = mapAxisConfig(xAxisConfig, 'x');

    // 应用轴配置
    applyAxisConfig(view.value, 'x', g2Config.axis?.x);

    // 映射并应用比例尺配置
    const scaleConfig = mapScaleConfig(xAxisConfig, 'x');
    if (Object.keys(scaleConfig).length > 0) {
      applyScaleConfig(view.value, scaleConfig);
    }

    // X轴反转通过 scale 配置处理

    isRender && render();
  };

  /**
   * 更新Y轴配置
   */
  const updateYAxisConfig = (yAxisConfig: Axis = <Axis>options.value?.yAxis, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    const g2Config = mapAxisConfig(yAxisConfig, 'y');

    // 应用轴配置
    applyAxisConfig(view.value, 'y', g2Config.axis?.y);

    // 获取Y轴字段名数组
    const yFields = widget.value.yField?.map((field: any) => field.fieldName).filter(Boolean) || [];

    // 获取图表数据
    const chartData = widgetData.value || [];

    // 传递数据信息到比例尺配置
    const scaleConfig = mapScaleConfig(yAxisConfig, 'y', chartData, yFields, widget.value.type);

    if (Object.keys(scaleConfig).length > 0) {
      applyScaleConfig(view.value, scaleConfig);
    }

    isRender && render();
  };

  /**
   * 更新图形样式配置
   */
  const updateMarkConfig = (markConfig: Mark = <Mark>options.value?.mark, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    // 获取字段信息
    const xField = widget.value.xField[0]?.fieldName || '';

    // 为每个系列分别应用样式
    series.value.forEach((serie: any, index: number) => {
      const yField = widget.value.yField[index]?.fieldName || '';
      const commonStyle = mapMarkConfig(markConfig, options.value, index);
      const markStyle = mapMarkStyle(markConfig, widget.value.type, options.value, index);
      // 应用图形配置
      applyMarkStyle(
        chart.value,
        view.value,
        serie,
        index,
        widget.value.type as ChartType,
        { ...commonStyle, ...markStyle },
        xField,
        yField
      );
    });

    isRender && render();
  };

  /**
   * 更新图例配置
   */
  const updateLegendConfig = (legendConfig: Legend = options.value.legend, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    const g2Config = mapLegendConfig(legendConfig, options.value);

    applyLegendConfig(view.value, g2Config.legend);

    isRender && render();
  };

  const updateLabelConfig = (labelConfig: TotalLabel = options.value.label) => {
    if (!chart.value || !view.value) {
      return;
    }
    const g2Config = mapLabelConfig(labelConfig, view.value, widget.value.yField, series.value);
    // 标签样式应用
    applyLabelConfig(series.value, g2Config.label, widget.value, options.value.label, view.value);

    render();
  };

  /**
   * 更新图表配置（保持数据不变）
   * 根据传入的配置项，调用对应的专门更新方法
   */
  const updateChartConfig = async (config: any = options.value, isRender: boolean = false) => {
    if (!chart.value) {
      return;
    }

    if (config.xAxis !== undefined) {
      updateXAxisConfig(config.xAxis, false);
    }

    if (config.yAxis !== undefined) {
      updateYAxisConfig(config.yAxis, false);
    }

    if (config.legend !== undefined) {
      updateLegendConfig(config.legend, false);
    }

    if (config.mark !== undefined) {
      await updateMarkConfig(config.mark, false);
    }

    updateLabelConfig(config.label);
    // 重新渲染
    isRender && render();
  };

  /**
   * 渲染图表
   */
  const render = async () => {
    if (!chart.value) {
      return;
    }
    await renderChart(chart.value);
  };

  /**
   * 更新图表数据
   */
  const updateData = async (data: any[] = widgetData.value) => {
    if (!chart.value) {
      return;
    }
    await updateSeries();
    await updateChartConfig(options.value, true);
    updateChartData(view.value, data);
  };

  const updateSeries = () => {
    if (!chart.value) {
      return;
    }
    view.value.children = [];
    series.value = createSeries();
  };

  /**
   * 调整图表大小
   */
  const resize = (width: number = block.value?.width, height: number = block.value?.height) => {
    if (!chart.value) {
      return;
    }
    resizeChart(chart.value, width, height);
   // console.log(getChartOptions(chart.value));
  };

  /**
   * 销毁图表
   */
  const destroy = () => {
    if (chart.value) {
      clear();
      destroyChart(chart.value);
      chart.value = undefined;
    }
  };

  const getSeries = (id?: string) => {
    if (!series.value) {
      return;
    }
    return id ? series.value.find((s: any) => s.id === id) : series.value;
  };

  const getView = () => {
    if (!chart.value) {
      return;
    }
    return getChartView(chart.value);
  };

  // 组件卸载时自动销毁图表
  onUnmounted(() => {
    destroy();
  });

  return {
    chart,
    view,
    series,
    init,
    configureChart,
    render,
    getView,
    getSeries,
    createSeries,
    updateSeries,
    updateData,
    updateChartConfig,
    updateXAxisConfig,
    updateYAxisConfig,
    updateMarkConfig,
    updateLegendConfig,
    updateLabelConfig,
    resize,
    destroy
  };
};
