import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { useFill, type OptionStyle } from '@vis/document-core';
/**
 * 下拉选项配置面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-option',
  props: {
    options: {
      type: Object as PropType<OptionStyle>,
      required: true
    }
  },
  setup(props, { emit }) {
    const { getFillStyle } = useFill();

    const computedOptions = computed(() => props.options);

    // 悬浮样式
    const hoverStyle = computed(() => {
      if (!computedOptions.value?.hoverFillPaints) return '';
      return getFillStyle(computedOptions.value.hoverFillPaints);
    });
    const hoverPopupRef = ref();
    const hoverPopupShow = ref(false);
    const showHoverPopup = (e: Event) => {
      e.stopPropagation();
      hoverPopupShow.value = true;
      hoverPopupRef.value?.handleShow(e);
    };

    // 选中样式
    const activeStyle = computed(() => {
      if (!computedOptions.value?.activeFillPaints) return '';
      return getFillStyle(computedOptions.value.activeFillPaints);
    });
    const activePopupRef = ref();
    const activePopupShow = ref(false);
    const showActivePopup = (e: Event) => {
      e.stopPropagation();
      activePopupShow.value = true;
      activePopupRef.value?.handleShow(e);
    };

    //#region 圆角
    /** 圆角 */
    const radiusLeft = ref<number | string>(computedOptions.value.radius[0]);
    const radiusRight = ref<number | string>(computedOptions.value.radius[1]);

    const radiusChange = () => {
      if (typeof radiusLeft.value === 'number') {
        computedOptions.value.radius[0] = radiusLeft.value;
        computedOptions.value.radius[3] = radiusLeft.value;
      }

      if (typeof radiusRight.value === 'number') {
        computedOptions.value.radius[1] = radiusRight.value;
        computedOptions.value.radius[2] = radiusRight.value;
      }
    };

    const showRadius = ref(false);

    const flagLeft = computed(() => computedOptions.value.radius[0] !== computedOptions.value.radius[3]);
    const flagRight = computed(() => computedOptions.value.radius[1] !== computedOptions.value.radius[2]);
    watch(
      [() => flagLeft.value, () => flagRight.value],
      () => {
        radiusLeft.value = flagLeft.value ? 'Mixed' : computedOptions.value.radius[0];
        radiusRight.value = flagRight.value ? 'Mixed' : computedOptions.value.radius[1];
      },
      {
        immediate: true
      }
    );

    //#endregion

    return {
      computedOptions,

      hoverStyle,
      hoverPopupRef,
      hoverPopupShow,
      showHoverPopup,

      activeStyle,
      activePopupRef,
      activePopupShow,
      showActivePopup,

      radiusLeft,
      radiusRight,
      radiusChange,
      showRadius
    };
  }
});
