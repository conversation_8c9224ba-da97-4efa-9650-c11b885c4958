import { isNumber } from 'lodash-es';
import { AdornType, BarMark, Effects, FillPaints, LineMark, Stroke, Image, Icon, Color } from '../..';

/**
 * 图形标记
 * <AUTHOR>
 */
export class BaseMark {
  /** 整体透明度 */
  opacity: number = 1;

  /** 描边样式 */
  stroke: Stroke = new Stroke();

  /** 效果 */
  effects: Effects = new Effects();

  /** 系列相关配置 */
  series: SeriesMark[] = [];

  constructor(stroke?: Stroke, effects?: Effects, series?: SeriesMark[]) {
    stroke && (this.stroke = stroke);
    effects && (this.effects = effects);
    series && (this.series = series);
  }
}
export class Mark extends BaseMark {
  /** 填充样式 */
  fillPaints: FillPaints = new FillPaints(undefined, new Color(58, 124, 255, 1));

  constructor(fillPaints?: FillPaints, stroke?: Stroke, effects?: Effects, series?: SeriesMark[]) {
    super(stroke, effects, series);
    fillPaints && (this.fillPaints = fillPaints);
  }
}

export class SeriesMark {
  /** 系列index */
  seriesIndex: number = 0;

  /** 系列id */
  id: string = '';

  /** 填充样式 */
  fillPaints?: FillPaints;

  /** 描边样式 */
  stroke: Stroke = new Stroke();

  constructor(id?: string, seriesIndex?: number, fillPaints?: FillPaints, stroke?: Stroke) {
    isNumber(seriesIndex) && (this.seriesIndex = seriesIndex);
    id && (this.id = id);
    fillPaints && (this.fillPaints = fillPaints);
    stroke && (this.stroke = stroke);
  }
}

/** 图表标记类型 */
export type ChartMark = BarMark | LineMark;

/**
 * 图表标记
 */
export class MarkSymbol {
  /**
   * 图标类型
   */
  type: AdornType = AdornType.Icon;

  /**
   * 图片
   */
  image: Image = new Image();

  /**
   * 图标
   */
  icon: Icon = new Icon();
}
