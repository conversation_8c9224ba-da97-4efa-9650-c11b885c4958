import { computed, nextTick } from 'vue';
import { useActionStore, useDesignStore } from '../stores';
import { useGraph } from './graph';
import { useConstraints } from './constraints';
import { usePage } from './page';
import {
  Block,
  useDocumentStore,
  Graph,
  Page,
  Frame,
  DirectionType,
  JustifyAlign,
  GraphType,
  ShapeType,
  useLayout,
  ResizeType,
  Constraints,
  GridItem,
  AutoLayout
} from '@vis/document-core';
import {
  DesignerMode,
  GraphPanel,
  MenuPanel,
  PropertyPanel,
  VIS_DESIGN_INFINITE_CANVAS,
  type ContextMenuInstance
} from '../models';
import { copyToClipboard, Notify } from 'quasar';
import { cloneDeep, isString } from 'lodash-es';
import { CacheService } from '@hetu/util';
import { AttachmentService, Keyboard, type ArrowDirection, type DigitType } from '@hetu/platform-shared';
import { thumbnail } from '../utils/thumbnail';

/**
 * 设计器画布操作方法
 * <AUTHOR>
 */
export const useAction = () => {
  const actionStore = useActionStore();
  const docStore = useDocumentStore();
  const designStore = useDesignStore();

  const actions = computed(() => actionStore.actions.value);
  const doc = computed(() => docStore.document.value);
  const counter = computed(() => ++designStore.counter.value);

  const {
    findGraph,
    filterGraph,
    findGraphParents,
    activeGraphs,
    getGraphXYByCanvas,
    moveGraphs,
    getCanvasPosSize,
    formatRotate,
    flattenPageFrames,
    getFrameByPosition
  } = useGraph();

  const { findPage, customClone, calcPageZoom } = usePage();

  const { isFlex, isGrid } = useLayout();

  const { setChildrenPosition } = useConstraints();

  const move = () => {
    resetGroupActive();
    resetGroupActive('shapes');
    actions.value.move.active = true;
  };
  const hand = () => {
    resetGroupActive();
    resetGroupActive('shapes');
    actions.value.hand.active = true;
  };

  const scale = () => {
    resetGroupActive();
    resetGroupActive('shapes');
    actions.value.scale.active = true;
    designStore.panelState.value.graph = GraphPanel.Style;
  };

  const frame = () => {
    resetGroupActive();
    resetGroupActive('shapes');
    actions.value.frame.active = true;
  };

  const textbox = () => {
    resetGroupActive();
    resetGroupActive('shapes');
    actions.value.textbox.active = true;
  };

  const shapes = (type: string) => {
    resetGroupActive();
    resetGroupActive('shapes');
    actions.value[type].active = true;
    actions.value.shapes.active = true;
  };

  const circle = () => shapes(ShapeType.Circle);
  const rectangle = () => shapes(ShapeType.Rectangle);

  const dhyana = () => {
    designStore.mode.value = DesignerMode.dhyana;
  };

  /** 设计模式 */
  const design = () => {
    resetGroupActive('mode');
    actions.value.design.active = true;
    designStore.mode.value = DesignerMode.Design;

    // 将等比缩放和文本禁用改为可用
    resetGroupActive();
    move();
  };

  /** 交互模式 */
  const interact = () => {
    resetGroupActive('mode');
    actions.value.interact.active = true;
    designStore.mode.value = DesignerMode.Interact;

    // 将等比缩放和文本禁用
    actions.value.textbox.disable = true;
    actions.value.scale.disable = true;
    if (actions.value.scale.active) {
      actions.value.move.active = true;
      actions.value.scale.active = false;
    }
  };

  /** 节点编程模式 */
  const node = () => {
    //  designStore.mode.value = DesignerMode.Node;
  };

  /**
   * 还原同组操作状态
   * @param group
   */
  const resetGroupActive = (group: string = 'mouse') => {
    Object.values(actions.value).forEach((ele) => {
      if (ele.group === group) {
        ele.active = false;
        ele.disable = false;
      }
    });
  };

  /**
   * 对齐
   * @param type
   */
  const align = (type: string) => {
    const moveableRef = designStore.moveableRef.value;
    const activeGraphs = designStore.active.value.graphs;
    if (moveableRef) {
      const rect = moveableRef.getRect();
      const moveables = moveableRef.getMoveables();
      if (moveables.length <= 1 || activeGraphs.length <= 1) {
        alignGraph(type);
        return;
      }

      const leftArray: Array<number> = [];
      const rightArray: Array<number> = [];
      const topArray: Array<number> = [];
      const bottomArray: Array<number> = [];

      let allWidth = 0;
      let allHeight = 0;
      let activeLength = 0;
      activeGraphs.forEach((g) => {
        const { width, height } = g;
        const left = g.transform.translate[0];
        const top = g.transform.translate[1];

        leftArray.push(left);
        rightArray.push(left + width);
        topArray.push(top);
        bottomArray.push(top + height);
        allWidth += width;
        allHeight += height;
        activeLength++;
      });

      switch (type) {
        case 'left':
          activeGraphs.forEach((g) => {
            const x = Math.min(...leftArray);
            g.transform.translate[0] = x;
          });
          break;
        case 'right':
          activeGraphs.forEach((g) => {
            const x = Math.max(...rightArray) - g.width;
            g.transform.translate[0] = x;
          });
          break;
        case 'top':
          activeGraphs.forEach((g) => {
            const y = Math.min(...topArray);
            g.transform.translate[1] = y;
          });
          break;
        case 'bottom':
          activeGraphs.forEach((g) => {
            const y = Math.max(...bottomArray) - g.height;
            g.transform.translate[1] = y;
          });
          break;
        case 'center':
          activeGraphs.forEach((g) => {
            const x = Math.round(Math.min(...leftArray) + rect.width / 2 - g.width / 2);
            g.transform.translate[0] = x;
          });
          break;
        case 'middle':
          activeGraphs.forEach((g) => {
            const y = Math.round(Math.min(...topArray) + rect.height / 2 - g.height / 2);
            g.transform.translate[1] = y;
          });
          break;
        case 'horizontalSpace':
          {
            let left = rect.left;

            if (moveables.length <= 1) {
              return;
            }
            const gap =
              (rect.width -
                rect.children!.reduce((prev, cur) => {
                  return prev + cur.width;
                }, 0)) /
              (moveables.length - 1);

            moveables.sort((a, b) => {
              return a.state.left - b.state.left;
            });

            // rect.left是基于最外层的位置，如果在容器内，需要减去容器的x
            const isParent = activeGraphs[0].parent;
            let pLeft = 0;
            if (isParent) {
              const parents = findGraphParents(activeGraphs[0].id);
              pLeft = parents.reduce((sum, p) => {
                return sum + p.transform.translate[0];
              }, 0);
            }

            moveables.forEach((children) => {
              const rect = children.getRect();
              const id = children.props.target?.id;
              const graph = activeGraphs.find((g) => g.id === id);
              if (graph) {
                const x = Math.round(isParent ? left - pLeft : left);
                graph.transform.translate[0] = x;
              }
              left += rect.width + gap;
            });
          }
          break;
        case 'verticalSpace':
          {
            let top = rect.top;

            if (moveables.length <= 1) {
              return;
            }
            const gap =
              (rect.height -
                rect.children!.reduce((prev, cur) => {
                  return prev + cur.height;
                }, 0)) /
              (moveables.length - 1);

            moveables.sort((a, b) => {
              return a.state.top - b.state.top;
            });

            // rect.left是基于最外层的位置，如果在容器内，需要减去容器的x
            const isParent = activeGraphs[0].parent;
            let pTop = 0;
            if (isParent) {
              const parents = findGraphParents(activeGraphs[0].id);
              pTop = parents.reduce((sum, p) => {
                return sum + p.transform.translate[1];
              }, 0);
            }

            moveables.forEach((children) => {
              const rect = children.getRect();
              const id = children.props.target?.id;
              const graph = activeGraphs.find((g) => g.id === id);
              if (graph) {
                const y = Math.round(isParent ? top - pTop : top);
                graph.transform.translate[1] = y;
              }

              top += rect.height + gap;
            });
          }
          break;
      }
    }
  };

  /**
   * 单图形对齐
   * @param type 对齐方式
   * @param graph 图形
   * @param {
   * pw: 父组件宽度
   * ph: 父组件高度
   * }
   */
  const singleAlign = (type: string, graph: Graph, { pw, ph }: { pw: number; ph: number }) => {
    switch (type) {
      case 'left':
        graph.transform.translate[0] = 0;
        break;
      case 'right':
        graph.transform.translate[0] = pw - graph.width;
        break;
      case 'top':
        graph.transform.translate[1] = 0;
        break;
      case 'bottom':
        graph.transform.translate[1] = ph - graph.height;
        break;
      case 'center':
        graph.transform.translate[0] = Math.round((pw - graph.width) / 2);
        break;
      case 'middle':
        graph.transform.translate[1] = Math.round((ph - graph.height) / 2);
        break;
    }
  };

  const multipleAlign = (type: string, graphs: Graph[]) => {
    const leftArray: Array<number> = [];
    const rightArray: Array<number> = [];
    const topArray: Array<number> = [];
    const bottomArray: Array<number> = [];

    let allWidth = 0;
    let allHeight = 0;
    let activeLength = 0;
    graphs.forEach((g) => {
      const left = g.transform.translate[0];
      const top = g.transform.translate[1];
      leftArray.push(left);
      rightArray.push(left + g.width);
      topArray.push(left);
      bottomArray.push(top + g.height);
      allWidth += g.width;
      allHeight += g.height;
      activeLength++;
    });

    switch (type) {
      case 'left':
        graphs.forEach((g) => {
          const x = Math.min(...leftArray);
          g.transform.translate[0] = x;
        });
        break;
      case 'right':
        graphs.forEach((g, i) => {
          const x = Math.max(...rightArray) - g.width;
          g.transform.translate[0] = x;
        });
        break;
      case 'top':
        graphs.forEach((g) => {
          const y = Math.min(...topArray);
          g.transform.translate[1] = y;
        });
        break;
      case 'bottom':
        graphs.forEach((g) => {
          const y = Math.max(...bottomArray) - g.height;
          g.transform.translate[1] = y;
        });
        break;
      case 'center':
        {
          const centerX = (Math.max(...rightArray) - Math.min(...leftArray)) / 2 + Math.min(...leftArray);
          graphs.forEach((g) => {
            const x = Math.round(centerX - g.width / 2);
            g.transform.translate[0] = x;
          });
        }
        break;
      case 'middle':
        {
          const centerY = (Math.max(...bottomArray) - Math.min(...topArray)) / 2 + Math.min(...topArray);
          graphs.forEach((g) => {
            const y = Math.round(centerY - (g.height as number) / 2);
            g.transform.translate[1] = y;
          });
        }
        break;
    }
  };

  /**
   * 选中单个图形对齐
   * 规则：1. 选中图形有父元素，对齐是基于父元素的
   *      2. 选中图形无父元素，对齐的是图形的子元素
   *      3. 选中图形无父元素、无子元素，不对齐
   */
  const alignGraph = (type: string) => {
    const moveableRef = designStore.moveableRef.value;
    const activeGraphs = designStore.active.value.graphs;
    if (moveableRef) {
      const moveables = moveableRef.getMoveables();
      if (moveables.length == 1 && activeGraphs.length == 1) {
        const graph = activeGraphs[0];
        if (graph.parent) {
          // 选中图形有父元素，对齐是基于父元素的
          const parent = findGraph(graph.parent);
          if (parent) {
            const pw = parent?.width || 0;
            const ph = parent?.height || 0;
            singleAlign(type, graph, { pw, ph });
          }
        } else {
          // 选中图形无父元素，对齐的是图形的子元素
          const len = graph.children?.length || 0;
          if (graph.children?.length) {
            if (len === 1) {
              // 只有一个图形时，基于选中图形对齐
              singleAlign(type, graph.children[0], { pw: graph.width, ph: graph.height });
            } else {
              // 多个图形时，多图形对齐
              multipleAlign(type, graph.children);
            }
          }
        }

        //moveableRef.updateRect();
      }
    }
  };

  /** 删除组件 */
  const deleteGraphs = () => {
    const graphs = designStore.active.value.graphs;
    if (!graphs.length) return;

    graphs.forEach((graph) => {
      const parentChildren = getParentChildren(graph);
      if ((graph as Block).decoration) {
        const cIndex = doc.value.blocks.findIndex((b) => b.id === (graph as Block).decoration);
        doc.value.blocks.splice(cIndex, 1);
      }
      const index = parentChildren.findIndex((g) => g.id === graph.id);
      parentChildren.splice(index, 1);
      parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
    });

    activeGraphs();
  };

  /** 重命名 */
  const renameGraph = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      designStore.canvasState.value.renaming = activeGraph.id;
    }
  };

  /** 锁定/解锁图层 */
  const lockOrUnlockGraph = () => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      activeGraphs.forEach((activeGraph) => {
        activeGraph.locked = !activeGraph.locked;
      });
    }
  };

  /** 显示/隐藏图层 */
  const showOrHideGraph = () => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      activeGraphs.forEach((activeGraph) => {
        activeGraph.visible = !activeGraph.visible;
      });
    }
  };

  /** 将图形定位在画布的中央位置 */
  const locationToCanvas = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      designStore.rulerState.value.zoom = 1;
      designStore.infiniteCanvasRef.value?.setZoom(1);
      const { x, y } = getGraphXYByCanvas(activeGraph);

      // 获取dock高度
      const dockDom = document.querySelector('.vis-design-dock') as HTMLDivElement;
      const dockHeight = dockDom ? dockDom.offsetHeight + 10 : 0;

      const canvasDom = document.querySelector(`#${VIS_DESIGN_INFINITE_CANVAS}`) as HTMLDivElement;
      const canvasWidth = canvasDom.offsetWidth;
      const canvasHeight = canvasDom.offsetHeight - dockHeight;
      const offsetX = (canvasWidth - activeGraph.width) / 2;
      const offsetY = (canvasHeight - activeGraph.height) / 2;
      designStore.infiniteCanvasRef.value?.scrollTo(x - offsetX, y - offsetY);
    }
  };

  /** 设置为主容器 */
  const setAsMainFrame = () => {
    const activeGraph = designStore.active.value.graph;
    const activePage = designStore.active.value.page;
    if (activeGraph && activePage) {
      const page = findPage(activePage.id);
      (page as Page).main = activeGraph.id;
    }
  };

  /** 设为封面 */
  const setAsCover = async () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      // 获取dom
      const thumbnailWrap = document.querySelector(`[id="${activeGraph.id}"]`) as HTMLDivElement;
      if (!thumbnailWrap) return;
      // 计算缩放
      const coverWidth = 216;
      const coverHeight = 121;
      const scale = Math.min(coverWidth / thumbnailWrap.offsetWidth, coverHeight / thumbnailWrap.offsetHeight);
      const img = await thumbnail(thumbnailWrap, {
        width: coverWidth,
        height: coverHeight,
        scale
      });
      // 本地存储
      CacheService.set('thumbnail', img.src);

      // 上传base64
      // actions.value.setAsCover.loading = true;
      // AttachmentService.uploadBase64(img.src, 'thumbnail').then((res) => {
      //   actions.value.setAsCover.loading = false;
      //   if (res && res.status === 'success') {
      //     docStore.document.value.thumbnail = res.data.id;
      //   }
      // });
    }
  };

  /** 计算区域极值 */
  const calcArea = (graphs: Graph[] | string[]) => {
    if (graphs.every((g) => isString(g))) {
      graphs = filterGraph(graphs as string[]);
    }
    let maxX = 0,
      maxY = 0,
      minX = 0,
      minY = 0;
    graphs.forEach((g, ind) => {
      const { x, y } = getGraphXYByCanvas(g);
      if (ind === 0) {
        // 初始值
        maxX = x + g.width;
        maxY = y + g.height;
        minX = x;
        minY = y;
      }
      // 计算极值
      maxX = Math.max(maxX, x + g.width);
      maxY = Math.max(maxY, y + g.height);
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
    });
    return {
      maxX,
      maxY,
      minX,
      minY,
      width: maxX - minX,
      height: maxY - minY
    };
  };

  /** 创建容器 */
  const createFrame = () => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      // 计算外层容器宽高
      const { maxX, maxY, minX, minY } = calcArea(activeGraphs);

      // 创建容器
      const frame = new Frame();
      frame.name = `容器 ${counter.value}`;
      frame.width = maxX - minX;
      frame.height = maxY - minY;
      // 计算容器位置
      const frameX = minX;
      const frameY = minY;

      // 判断是否为同级别
      const isSameLevel = activeGraphs.every((g) => g.parent === activeGraphs[0].parent);
      let target: Graph;
      if (isSameLevel) {
        target = activeGraphs[0];
      } else {
        // 非同级别，需要找到距离根级最近的图形
        target = activeGraphs.reduce((a, b) => {
          const aParents = findGraphParents(a.id);
          const bParents = findGraphParents(b.id);
          return aParents.length <= bParents.length ? a : b;
        });
      }
      const targetId = target.parent || VIS_DESIGN_INFINITE_CANVAS;

      activeGraphs.forEach((graph) => {
        // 重新计算图形在容器内位置
        const { x, y } = getGraphXYByCanvas(graph);
        graph.transform.translate[0] = x - frameX;
        graph.transform.translate[1] = y - frameY;

        // 查找父级
        let parent: any;
        let parentChildren: Graph[] = [];
        if (!graph.parent) {
          parentChildren = designStore.active.value.page.children;
        } else {
          parent = findGraph(graph.parent);
          parentChildren = parent.children;
        }
        // 在父级中删除图形
        const index = parentChildren.findIndex((item) => item.id === (<Block | Frame>graph).id);
        if (index !== -1) {
          parentChildren.splice(index, 1);
          parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        }

        // 维护父子关系
        graph.parent = frame.id;
        frame.children.push(<Block | Frame>graph);
      });

      // 移动容器到目标位置
      frame.transform.translate[0] = frameX;
      frame.transform.translate[1] = frameY;
      moveGraphs([frame], targetId, 0);
    }
  };

  /** 取消容器 */
  const cancelFrame = () => {
    const _activeGraphs = designStore.active.value.graphs;
    if (_activeGraphs.length) {
      _activeGraphs.forEach((activeGraph) => {
        // 查找父级
        let parent: any;
        let parentChildren: Graph[] = [];
        let targetId: string = '';
        if (!activeGraph.parent) {
          parentChildren = designStore.active.value.page.children;
          targetId = VIS_DESIGN_INFINITE_CANVAS;
        } else {
          parent = findGraph(activeGraph.parent);
          parentChildren = parent.children;
          targetId = parent.id;
        }

        // 移动子级到当前容器
        if (activeGraph.children?.length) {
          const insertIndex = parentChildren.findIndex((item) => item.id === (<Block | Frame>activeGraph).id);
          moveGraphs([...activeGraph.children], targetId, insertIndex);
        } else {
          activeGraphs();
        }

        // 在父级中删除容器
        const index = parentChildren.findIndex((item) => item.id === (<Block | Frame>activeGraph).id);
        if (index !== -1) {
          parentChildren.splice(index, 1);
          parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        }
      });
    }
  };
  /**
   * 旋转
   * @param direction 方向 positive 正向 negative 负向
   *  */
  const rotate = (direction: 'positive' | 'negative') => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      const STEP = 90;
      activeGraphs.forEach((activeGraph) => {
        if (direction === 'positive') {
          activeGraph.transform.rotate = formatRotate(activeGraph.transform.rotate + STEP);
        } else {
          activeGraph.transform.rotate = formatRotate(activeGraph.transform.rotate - STEP);
        }
      });
    }
  };
  const rotatePositiveStep = () => rotate('positive');
  const rotateNegativeStep = () => rotate('negative');

  /** 查找父级列表 */
  const getParentChildren = (graph: Graph | string) => {
    if (isString(graph)) {
      graph = findGraph(graph) as Graph;
    }
    // 查找父级
    let parent: any;
    let parentChildren: Graph[] = [];
    if (!graph.parent) {
      parentChildren = designStore.active.value.page.children;
    } else {
      parent = findGraph(graph.parent);
      parentChildren = parent.children;
    }
    return parentChildren;
  };

  /**
   * 移动
   * @param direction 方向 up 上移一层 down 下移一层 top 移至顶层 bottom 移至底层
   * */
  const moveGraph = (direction: 'up' | 'down' | 'top' | 'bottom') => {
    const _activeGraphs = designStore.active.value.graphs;
    if (_activeGraphs.length) {
      _activeGraphs.forEach((activeGraph, activeIndex) => {
        // 正序查找图形位置
        const parentChildren = getParentChildren(activeGraph);
        const index = parentChildren.findIndex((item) => item.id === activeGraph.id);

        // 倒序查找图形位置
        const lastActiveGraph = _activeGraphs[_activeGraphs.length - 1 - activeIndex];
        const lastParentChildren = getParentChildren(lastActiveGraph);
        const lastIndex = lastParentChildren.findIndex((item) => item.id === lastActiveGraph.id);

        if (direction === 'up') {
          // 达到顶层或者上一个元素为激活状态，退出
          if (index === 0 || _activeGraphs.some((g) => g.id === parentChildren[index - 1].id)) {
            return;
          }
          if (index !== -1) {
            parentChildren.splice(index, 1);
          }
          parentChildren.splice(index - 1, 0, activeGraph);
        } else if (direction === 'down') {
          // 达到底层或者下一个元素为激活状态，退出
          if (
            lastIndex === lastParentChildren.length - 1 ||
            _activeGraphs.some((g) => g.id === lastParentChildren[lastIndex + 1].id)
          ) {
            return;
          }
          if (lastIndex !== -1) {
            lastParentChildren.splice(lastIndex, 1);
          }
          lastParentChildren.splice(lastIndex + 1, 0, lastActiveGraph);
        } else if (direction === 'top') {
          if (lastIndex === 0) {
            return;
          }
          if (lastIndex !== -1) {
            lastParentChildren.splice(lastIndex, 1);
          }
          lastParentChildren.unshift(lastActiveGraph);
        } else if (direction === 'bottom') {
          if (index === parentChildren.length - 1) {
            return;
          }
          if (index !== -1) {
            parentChildren.splice(index, 1);
          }
          parentChildren.push(activeGraph);
        }

        // 更新order
        parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        lastParentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
      });
    }
  };

  /** 同层级上移一层 */
  const moveUpGraph = () => moveGraph('up');
  /** 同层级下移一层 */
  const moveDownGraph = () => moveGraph('down');
  /** 同层级移到顶层 */
  const moveTopGraph = () => moveGraph('top');
  /** 同层级移到底层 */
  const moveBottomGraph = () => moveGraph('bottom');
  /**
   * 按照步进移动图层
   * @param isBig 是否为大步进
   * @param direction 移动方向
   */
  const moveSteps = (isBig: boolean, direction: ArrowDirection) => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      const STEP = isBig ? 10 : 1;
      activeGraphs.forEach((activeGraph) => {
        if (direction === Keyboard.ArrowUp) {
          activeGraph.transform.translate[1] -= STEP;
        } else if (direction === Keyboard.ArrowDown) {
          activeGraph.transform.translate[1] += STEP;
        } else if (direction === Keyboard.ArrowLeft) {
          activeGraph.transform.translate[0] -= STEP;
        } else if (direction === Keyboard.ArrowRight) {
          activeGraph.transform.translate[0] += STEP;
        }
      });
    }
  };
  /** 大步进移动图层 步长为10px */
  const moveBigSteps = (direction: ArrowDirection) => moveSteps(true, direction);
  /** 小步进移动图层 步长为1px */
  const moveSmallSteps = (direction: ArrowDirection) => moveSteps(false, direction);

  /** 按照步进调整宽高 */
  const resizeSteps = (isBig: boolean, direction: ArrowDirection) => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      const STEP = isBig ? 10 : 1;
      activeGraphs.forEach((activeGraph) => {
        if (direction === Keyboard.ArrowUp) {
          activeGraph.height = Math.max(activeGraph.height - STEP, 1);
        } else if (direction === Keyboard.ArrowDown) {
          activeGraph.height = activeGraph.height + STEP;
        } else if (direction === Keyboard.ArrowLeft) {
          activeGraph.width = Math.max(activeGraph.width - STEP, 1);
        } else if (direction === Keyboard.ArrowRight) {
          activeGraph.width = activeGraph.width + STEP;
        }
      });
    }
  };
  /** 大步进调整宽高 步长为10px */
  const resizeGraphBigSteps = (direction: ArrowDirection) => resizeSteps(true, direction);
  /** 小步进调整宽高 步长为1px */
  const resizeGraphSmallSteps = (direction: ArrowDirection) => resizeSteps(false, direction);

  /** 设置图层透明度 */
  const setGraphOpacity = (opacity: DigitType) => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      activeGraphs.forEach((activeGraph) => {
        const value = parseFloat(opacity.replace('Digit', '')) * 10;
        activeGraph.opacity = value || 100;
      });
    }
  };

  /** 对齐 */
  const alignLeft = () => align('left');
  const alignCenter = () => align('center');
  const alignRight = () => align('right');
  const alignTop = () => align('top');
  const alignMiddle = () => align('middle');
  const alignBottom = () => align('bottom');
  const alignVerticalAverage = () => align('verticalSpace');
  const alignHorizontalAverage = () => align('horizontalSpace');

  /** 设置自动布局 */
  const setAutoLayout = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph && activeGraph.type === GraphType.Frame) {
      const frame = activeGraph as Frame;
      frame.autoLayout.direction =
        frame.autoLayout.direction === DirectionType.Freeform ? DirectionType.Horizontal : DirectionType.Freeform;

      const autoLayout = frame.autoLayout;

      if ([DirectionType.Horizontal, DirectionType.Vertical].includes(autoLayout.direction)) {
        autoLayout.mainAxisAlignment = JustifyAlign.Start;
        autoLayout.crossAxisAlignment = JustifyAlign.Start;
      }

      const isFreeform = autoLayout.direction === DirectionType.Freeform;
      const flex = isFlex(frame);
      const grid = isGrid(frame);
      if (isFreeform) {
        frame.limitSize.width.resize = ResizeType.Fixed;
        frame.limitSize.height.resize = ResizeType.Fixed;
      }
      if (grid) {
        autoLayout.flowWarp = false;

        if (frame?.children.length) {
          frame.autoLayout.gridSize = [1, frame.children.length];
          frame.autoLayout.gridRowsSizing = ['Auto'];
          frame.autoLayout.gridColumnsSizing = Array(frame.children.length).fill('Auto');
        }
      }

      // 非自由布局时去掉子级的约束
      frame?.children.forEach((g, i) => {
        g.constraints = isFreeform ? new Constraints() : undefined;
        // gird布局默认添加一行n列
        if (grid) {
          g.gridItem = new GridItem([1, 2], [i + 1, i + 2]);
        } else {
          g.gridItem = undefined;
        }

        if (flex || grid) {
          g.limitSize.width.resize = ResizeType.Fixed;
          g.limitSize.height.resize = ResizeType.Fixed;

          nextTick(() => setChildrenPosition(frame));
        }
      });
    }
  };
  /** 添加自动布局 */
  const addAutoLayout = () => setAutoLayout();
  /** 取消自动布局 */
  const cancelAutoLayout = () => setAutoLayout();

  /** 复制图形ID */
  const copyGraphId = () => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      const text = activeGraphs.map((g) => g.id).join(',');
      copyToClipboard(text)
        .then(() => {
          Notify.create({
            classes: 'vis-design-notify',
            position: 'top',
            type: 'positive',
            message: '复制成功'
          });
        })
        .catch(() => {
          Notify.create({
            classes: 'vis-design-notify',
            position: 'top',
            type: 'negative',
            message: '复制失败'
          });
        });
    }
  };

  /** 设置复制内容 */
  const setClipboard = () => {
    const activeGraphs = designStore.active.value.graphs;
    if (activeGraphs.length) {
      designStore.canvasState.value.clipboard = activeGraphs.map((g) => g.id);
    }
  };
  /** 复制 */
  const copyGraph = () => setClipboard();
  /** 复制图形样式 */
  const copyGraphStyle = () => setClipboard();
  /** 复制图形配置 */
  const copyGraphConfig = () => setClipboard();

  /** 粘贴 */
  const pasteGraph = () => {
    const clipboard = designStore.canvasState.value.clipboard;
    const _activeGraphs = designStore.active.value.graphs;

    if (clipboard.length) {
      const GAP = 20;
      const activeIds: string[] = [];
      const { minX, minY, width, height } = calcArea(clipboard);

      if (_activeGraphs.length && !_activeGraphs.every((g) => clipboard.includes(g.id))) {
        _activeGraphs.forEach((activeGraph) => {
          const clonedGraphs: Graph[] = [];
          clipboard.forEach((clipboardId) => {
            const clipboardGraph = findGraph(clipboardId);
            const { x: canvasX, y: canvasY } = getGraphXYByCanvas(clipboardGraph as Graph);
            const clonedGraph = customClone(clipboardGraph as Graph);
            // 计算位置
            const gapX = canvasX - minX;
            const gapY = canvasY - minY;
            clonedGraph.transform.translate[0] = activeGraph.transform.translate[0] + gapX;
            clonedGraph.transform.translate[1] = activeGraph.transform.translate[1] + gapY;
            // 添加图形
            clonedGraphs.push(clonedGraph);
            // 记录新图形id
            activeIds.push(clonedGraph.id);
          });
          moveGraphs(clonedGraphs, activeGraph.id, 0);
        });
      } else {
        const clonedGraphs: Graph[] = [];
        clipboard.forEach((clipboardId) => {
          const clipboardGraph = findGraph(clipboardId);
          const { x: canvasX, y: canvasY } = getGraphXYByCanvas(clipboardGraph as Graph);
          const clonedGraph = customClone(clipboardGraph as Graph);
          const pasteX = canvasX + width + GAP;
          const pasteY = canvasY;
          clonedGraph.transform.translate[0] = pasteX;
          clonedGraph.transform.translate[1] = pasteY;
          // 添加图形
          clonedGraphs.push(clonedGraph);
          // 记录新图形id
          activeIds.push(clonedGraph.id);
        });

        // 计算放置区域位置
        const areaX = minX + width + GAP;
        const areaY = minY;

        // 计算添加到的容器
        flattenPageFrames();
        // 计算拖动时要放置的容器
        const hitFrame = getFrameByPosition(areaX + 1, areaY + 1);
        designStore.canvasState.value.frame = hitFrame;

        if (hitFrame) {
          // 添加到其他容器内
          moveGraphs(clonedGraphs, hitFrame.id, 0);
        } else {
          // 添加到根级
          const pageChildren = designStore.active.value.page?.children;
          pageChildren.unshift(...(clonedGraphs as any));
          pageChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        }
      }

      nextTick(() => {
        activeGraphs(activeIds);
      });
    }
  };

  /** 粘贴图形样式 Graph (包括frame textbox) */
  const pasteGraphStyle = () => {
    // 外观（透明度，圆角），文本（textbox），填充，描边，特效，布局网格（容器特有）
    const clipboard = designStore.canvasState.value.clipboard[0];
    const activeGraphs = designStore.active.value.graphs;
    if (clipboard && activeGraphs.length) {
      activeGraphs.forEach((activeGraph) => {
        const copyConfigGraph = findGraph(clipboard);
        // 图形不存在或者非容器和文本框类型或者自身，直接返回
        if (
          !copyConfigGraph ||
          ![GraphType.TextBox, GraphType.Frame].includes(activeGraph.type) ||
          clipboard === activeGraph.id
        ) {
          return;
        }
        // 透明度 opacity
        activeGraph.opacity = copyConfigGraph.opacity;
        // 圆角 radius
        activeGraph.radius = [...copyConfigGraph.radius];
        // 文本 text
        if (activeGraph.type === GraphType.TextBox && copyConfigGraph.text) {
          activeGraph.text = cloneDeep(copyConfigGraph.text);
        }
        // 填充 fillPaints
        if (copyConfigGraph.fillPaints) {
          activeGraph.fillPaints = cloneDeep(copyConfigGraph.fillPaints);
        }
        // 描边 stroke
        if (copyConfigGraph.stroke) {
          activeGraph.stroke = cloneDeep(copyConfigGraph.stroke);
        }
        // 特效 effects
        if (copyConfigGraph.effects) {
          activeGraph.effects = cloneDeep(copyConfigGraph.effects);
        }
        // 布局网格 layoutGuide
        if (activeGraph.type === GraphType.Frame && (<Frame>copyConfigGraph).layoutGuide) {
          (<Frame>activeGraph).layoutGuide = cloneDeep((<Frame>copyConfigGraph).layoutGuide);
        }
      });
    }
  };

  /** 粘贴图形配置 BLOCK */
  const pasteGraphConfig = () => {
    // 支持同类型BLOCK粘贴配置
    const clipboard = designStore.canvasState.value.clipboard[0];
    const activeGraphs = designStore.active.value.graphs;
    if (clipboard && activeGraphs.length) {
      activeGraphs.forEach((activeGraph) => {
        const copyConfigGraph = findGraph(clipboard);
        // 图形不存在或者非BLOCK类型或者自身，直接返回
        if (!copyConfigGraph || activeGraph.type !== GraphType.Block || clipboard === activeGraph.id) return;
        const copyBlock = doc.value.blocks.find((b) => b.id === (copyConfigGraph as Block).decoration);
        const targetBlock = doc.value.blocks.find((b) => b.id === (activeGraph as Block).decoration);
        if (!copyBlock || !targetBlock || copyBlock.type !== targetBlock.type) return;
        // 覆盖配置
        targetBlock.options = cloneDeep(copyBlock.options);
      });
    }
  };

  /** 粘贴并替换 */
  const pasteAndReplaceGraph = () => {
    const _activeGraphs = designStore.active.value.graphs;
    const clipboard = designStore.canvasState.value.clipboard;

    if (_activeGraphs.length && clipboard.length) {
      const activeIds: string[] = [];
      _activeGraphs.forEach((activeGraph) => {
        // 获取图形在画布位置
        const { x: activeX, y: activeY } = getGraphXYByCanvas(activeGraph as Graph);
        // 查找父级
        const parentChildren = getParentChildren(activeGraph);
        const targetId = activeGraph.parent || VIS_DESIGN_INFINITE_CANVAS;

        // 删除原图形
        const index = parentChildren.findIndex((item) => item.id === (<Graph>activeGraph).id);
        if (index !== -1) {
          parentChildren.splice(index, 1);
          parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        }

        const { minX, minY } = calcArea(clipboard);
        const clonedGraphs: Graph[] = [];

        clipboard.forEach((clipboardId) => {
          const clipboardGraph = findGraph(clipboardId) as Graph;
          const { x: canvasX, y: canvasY } = getGraphXYByCanvas(clipboardGraph as Graph);
          const clonedGraph = customClone(clipboardGraph);
          // 计算间距
          const gapX = canvasX - minX;
          const gapY = canvasY - minY;
          clonedGraph.transform.translate[0] = activeX + gapX;
          clonedGraph.transform.translate[1] = activeY + gapY;
          // 添加图形
          clonedGraphs.push(clonedGraph);
          // 记录新图形id
          activeIds.push(clonedGraph.id);
        });

        // 移动新图形
        const insertIndex = parentChildren.findIndex((item) => item.id === (<Graph>activeGraph).id);
        moveGraphs(clonedGraphs, targetId, insertIndex);
      });

      nextTick(() => {
        activeGraphs(activeIds);
      });
    }
  };

  /** 粘贴到鼠标位置 */
  const pasteToMousePosition = () => {
    const clipboard = designStore.canvasState.value.clipboard;
    if (clipboard.length) {
      const clonedGraphs: Graph[] = [];
      // 计算极值
      const { minX, minY } = calcArea(clipboard);
      // 鼠标位置
      const mousePosition = (designStore.contextMenuRef.value as ContextMenuInstance).position;
      const { x: mouseX, y: mouseY } = getCanvasPosSize(mousePosition.x, mousePosition.y);
      clipboard.forEach((clipboardId) => {
        const clipboardGraph = findGraph(clipboardId) as Graph;
        const { x: canvasX, y: canvasY } = getGraphXYByCanvas(clipboardGraph as Graph);
        const clonedGraph = customClone(clipboardGraph);
        // 计算间距
        const gapX = canvasX - minX;
        const gapY = canvasY - minY;
        clonedGraph.transform.translate[0] = mouseX + gapX;
        clonedGraph.transform.translate[1] = mouseY + gapY;
        // 添加图形
        clonedGraph.parent = '';
        clonedGraphs.push(clonedGraph);
      });

      // 计算添加到的容器
      flattenPageFrames();
      // 计算拖动时要放置的容器
      const hitFrame = getFrameByPosition(mouseX, mouseY);
      designStore.canvasState.value.frame = hitFrame;

      if (hitFrame) {
        // 添加到其他容器内
        moveGraphs(clonedGraphs, hitFrame.id, 0);
      } else {
        // 添加到根级
        const pageChildren = designStore.active.value.page?.children;
        pageChildren.unshift(...(clonedGraphs as any));
        pageChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
      }

      nextTick(() => {
        activeGraphs(clonedGraphs);
      });
    }
  };

  /** 全选 */
  const selectGraphAll = () => {
    const graphs = designStore.active.value.page.children;
    if (!graphs.length) return;
    // 过滤锁定/隐藏的图形
    const activeIds = graphs.filter((g: Graph) => !g.locked && g.visible).map((g: Graph) => g.id);
    nextTick(() => {
      activeGraphs(activeIds);
    });
  };

  /** 反选 */
  const selectGraphInverse = () => {
    const graphs = designStore.active.value.page.children;
    if (!graphs.length) return;
    const activeIds = graphs.filter((g: Graph) => !g.locked && g.visible).map((g: Graph) => g.id);
    const inactiveIds = activeIds.filter((id) => !designStore.active.value.graphIds.includes(id));
    nextTick(() => {
      activeGraphs(inactiveIds);
    });
  };
  /** 取消选中 */
  const escape = () => {
    const graphs = designStore.active.value.page.children;
    if (graphs.length) return activeGraphs();
  };

  /** 显示/隐藏操作面板 */
  const showOrHideOperationPanel = () => {
    actions.value.operationPanel.active = !actions.value.operationPanel.active;
  };
  /** 操作面板 */
  const operationPanel = () => showOrHideOperationPanel();

  /** 返回 */
  const back = () => {
    // todo
  };

  /** 新建文件 */
  const createFile = () => {
    // todo
  };

  /** 导出文件 */
  const exportFile = () => {
    // todo
  };

  /** 导入文件 */
  const importFile = () => {
    // todo
  };

  /** 创建文件副本 */
  const createFileCopy = () => {
    // todo
  };

  /** 保存快照 */
  const saveSnapshot = () => {
    // todo
  };

  /** 查看历史快照 */
  const viewSnapshots = () => {
    // todo
  };

  /** 查看历史版本 */
  const viewHistory = () => {
    // todo
  };

  /** 预览 */
  const preview = () => {
    // todo
  };

  /** 发布 */
  const release = () => {
    // todo
  };

  /** 查看发布历史 */
  const viewReleaseHistory = () => {
    // todo
  };

  /** 重命名 */
  const renameFile = () => (designStore.canvasState.value.renaming = docStore.document.value.id);

  /** 删除 */
  const deleteFile = () => {
    // todo
  };

  /** 撤销 */
  const undo = () => {
    // todo
  };

  /** 恢复 */
  const restore = () => {
    // todo
  };

  /** 标尺 */
  const ruler = () => {
    actions.value.ruler.active = !actions.value.ruler.active;
    designStore.rulerState.value.horizontalPos = actions.value.ruler.active ? 20 : 0;
    designStore.rulerState.value.verticalPos = actions.value.ruler.active ? 20 : 0;
  };

  /** 辅助线 */
  const guides = () => {
    actions.value.guides.active = !actions.value.guides.active;
    designStore.active.value.page.guides.visible = !designStore.active.value.page.guides.visible;
    // 加载辅助线
    const guides = designStore.active.value.page.guides;
    if (guides.visible) {
      designStore.horizontalGuidesRef.value?.loadGuides(guides.horizontal);
      designStore.verticalGuidesRef.value?.loadGuides(guides.vertical);
    } else {
      designStore.horizontalGuidesRef.value?.loadGuides([]);
      designStore.verticalGuidesRef.value?.loadGuides([]);
    }
  };

  /** 文件面板 */
  const filePanel = () => (designStore.panelState.value.menu = MenuPanel.File);
  /** 图表面板 */
  const chartPanel = () => (designStore.panelState.value.menu = MenuPanel.Chart);
  /** 组件面板 */
  const controlPanel = () => (designStore.panelState.value.menu = MenuPanel.Control);
  /** 素材面板 */
  const materialPanel = () => (designStore.panelState.value.menu = MenuPanel.Material);
  /** 设计面板 */
  const designPanel = () => (designStore.panelState.value.property = PropertyPanel.Design);
  /** 交互面板 */
  const interactivePanel = () => (designStore.panelState.value.property = PropertyPanel.Interactive);

  /** 素材库 */
  const materialLib = () => {
    // todo
  };

  /** 模板库 */
  const templateLib = () => {
    // todo
  };

  /** 编辑数据 */
  const editData = () => {
    // todo
  };

  /** 缩放步长 */
  const STEP = 25;
  /** 缩放比例 */
  const ratio = (value: number) => {
    return value / 100;
  };
  /** 设置缩放比例 */
  const setZoom = (zoom: number) => {
    const min = designStore.rulerState.value.zoomRange[0];
    const max = designStore.rulerState.value.zoomRange[1];
    zoom = Math.min(Math.max(zoom, min), max);
    designStore.rulerState.value.zoom = zoom;
    designStore.infiniteCanvasRef.value?.setZoom(zoom);
  };
  /** 放大 */
  const zoomIn = () => setZoom(designStore.rulerState.value.zoom + ratio(STEP));
  /** 缩小 */
  const zoomOut = () => setZoom(designStore.rulerState.value.zoom - ratio(STEP));
  /** 缩放到50% */
  const zoom50 = () => setZoom(ratio(50));
  /** 缩放到100% */
  const zoom100 = () => setZoom(ratio(100));
  /**  缩放到120% */
  const zoom120 = () => setZoom(ratio(120));
  /** 缩放到150% */
  const zoom150 = () => setZoom(ratio(150));
  /** 缩放到全部 */
  const zoomAll = () => calcPageZoom(designStore.active.value.page);
  /** 缩放到选中 */
  const zoomSelected = () => locationToCanvas();

  /** AI */
  const ai = () => {
    // todo
  };

  /** 偏好设置 */
  const preferences = () => {
    // todo
  };

  /** 查看快捷键 */
  const shortcuts = () => {
    actions.value.shortcuts.active = !actions.value.shortcuts.active;
  };

  /** 帮助 */
  const help = () => {
    // todo
  };

  /** 教程 */
  const tutorial = () => {
    // todo
  };

  const save = () => {
    console.log('保存:', docStore.document.value);
    CacheService.set(`doc_${docStore.document.value.id}`, docStore.document.value);
  };

  return {
    move,
    hand,
    scale,
    frame,
    textbox,

    shapes,
    circle,
    rectangle,

    dhyana,
    design,
    interact,
    node,

    align,

    deleteGraphs,
    renameGraph,
    lockOrUnlockGraph,
    showOrHideGraph,
    locationToCanvas,
    setAsMainFrame,
    setAsCover,
    createFrame,
    cancelFrame,
    rotatePositiveStep,
    rotateNegativeStep,
    moveUpGraph,
    moveDownGraph,
    moveTopGraph,
    moveBottomGraph,
    moveBigSteps,
    moveSmallSteps,
    resizeGraphBigSteps,
    resizeGraphSmallSteps,
    setGraphOpacity,
    alignLeft,
    alignCenter,
    alignRight,
    alignTop,
    alignMiddle,
    alignBottom,
    alignVerticalAverage,
    alignHorizontalAverage,
    addAutoLayout,
    cancelAutoLayout,
    copyGraph,
    pasteGraph,
    copyGraphId,
    copyGraphStyle,
    copyGraphConfig,
    pasteGraphStyle,
    pasteGraphConfig,
    pasteAndReplaceGraph,
    pasteToMousePosition,
    selectGraphAll,
    selectGraphInverse,
    escape,
    showOrHideOperationPanel,

    operationPanel,
    back,
    createFile,
    exportFile,
    importFile,
    createFileCopy,
    saveSnapshot,
    viewSnapshots,
    viewHistory,
    preview,
    release,
    viewReleaseHistory,
    renameFile,
    deleteFile,

    undo,
    restore,
    ruler,
    guides,
    filePanel,
    chartPanel,
    controlPanel,
    materialPanel,
    designPanel,
    interactivePanel,

    materialLib,
    templateLib,
    editData,

    setZoom,
    zoomIn,
    zoomOut,
    zoom50,
    zoom100,
    zoom120,
    zoom150,
    zoomAll,
    zoomSelected,

    ai,
    preferences,
    shortcuts,
    help,
    tutorial,

    save
  };
};
