import { defineComponent, ref, type PropType, computed, watch } from 'vue';
import {
  AxisField,
  Block,
  Graph,
  Mark,
  SeriesMark,
  WidgetBlock,
  useWidgetConfigStore,
  type WidgetType,
  FillPaints,
  Color,
  Stroke,
  useDocumentStore,
  ChartType,
  ChartGroup
} from '@vis/document-core';
import VisBarStripPopup from '../bar/bar-popup/bar-popup.vue';
import { useDesignStore } from '../../../../../../../../stores';
import { useWidget } from '../../../../../../../../hooks';

/**
 * 图形弹窗
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-mark-popup',
  components: {
    VisBarStripPopup
  },
  props: {
    options: {
      type: Object as PropType<Mark>,
      required: true
    },
    isSeries: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    const activeSeriesIndex = ref(0);

    const seriesOptions = ref<{ id: string; label: string; value: number }[]>([]);

    const seriesName = computed(() => {
      return seriesOptions.value[activeSeriesIndex.value].label;
    });

    const { active } = useDesignStore();
    const { getWidgetSubGroup } = useWidget();

    // 获取当前选中的组件
    const activeBlock = computed(() => active.value.block as WidgetBlock);

    const chartType = computed(() => {
      return activeBlock.value?.type as ChartType;
    });

    const chartGroup = computed(() => {
      return getWidgetSubGroup(activeBlock.value?.type as WidgetType) as ChartGroup;
    });

    const { dataConfigs } = useWidgetConfigStore();
    const config = dataConfigs.get(activeBlock.value?.type as WidgetType);
    const seriesFields = computed(() => {
      const seriesFieldKey = config && 'seriesFieldKey' in config ? config.seriesFieldKey : undefined;

      return seriesFieldKey ? activeBlock.value?.[seriesFieldKey as keyof WidgetBlock] : [];
    });

    const series = computed({
      get() {
        return props.options.series;
      },
      set(value) {
        Object.assign(props.options.series, value);
      }
    });

    const palettes = [
      new Color(54, 98, 236, 1),
      new Color(0, 181, 120, 1),
      new Color(6, 135, 153, 1),
      new Color(138, 56, 245, 1),
      new Color(87, 3, 197, 1),
      new Color(255, 30, 30, 1),
      new Color(255, 195, 0, 1),
      new Color(102, 90, 117, 1),
      new Color(54, 98, 236, 0.6),
      new Color(0, 181, 120, 0.5),
      new Color(6, 185, 153, 0.7),
      new Color(138, 56, 245, 0.7),
      new Color(87, 3, 197, 0.7),
      new Color(255, 30, 30, 0.7),
      new Color(255, 195, 0, 0.7),
      new Color(102, 90, 117, 0.7),
      new Color(255, 30, 30, 0.3),
      new Color(255, 195, 0, 0.3),
      new Color(102, 90, 117, 0.3),
      new Color(54, 98, 236, 0.3),
      new Color(0, 181, 120, 0.3),
      new Color(6, 185, 153, 0.3)
    ];

    const createSeriesMark = (item: AxisField, index: number, id?: string) => {
      if (chartType.value === ChartType.Line) {
        return new SeriesMark(
          id || item.fieldName!,
          index,
          undefined,
          new Stroke([1, 1, 1, 1], new FillPaints(undefined, palettes[index]))
        );
      } else {
        return new SeriesMark(
          id || item.fieldName!,
          index,
          new FillPaints(undefined, palettes[index]),
          new Stroke([0, 0, 0, 0], new FillPaints(undefined, palettes[index]))
        );
      }
    };
    /**
     * 处理度量系列
     */
    const handleSeries = () => {
      if (seriesFields.value && seriesFields.value.length) {
        seriesOptions.value = [];
        seriesFields.value.forEach((item: AxisField, index: number) => {
          seriesOptions.value.push({
            id: item.fieldName!,
            label: item.fieldAlias!,
            value: index
          });
        });
        if (series.value && series.value.length) {
          const newSeries: SeriesMark[] = [];

          seriesFields.value.forEach((item: AxisField, index: number) => {
            const seriesMark = props.options.series.find((sMark: SeriesMark) => sMark.id === item.fieldName);
            if (seriesMark) {
              newSeries[index] = seriesMark;
            } else {
              newSeries[index] = createSeriesMark(item, index);
            }
          });

          series.value = newSeries;
        } else {
          seriesFields.value.map((item: AxisField, index: number) => series.value.push(createSeriesMark(item, index)));
        }
        activeSeriesIndex.value = 0;
      }
    };

    // 根据图表类型启用不同的 watch 监听器
    const setupWatchers = () => {
      if ([ChartType.Bar, ChartType.Line].includes(chartType.value)) {
        watch(
          () => activeBlock.value?.id,
          () => {
            handleSeries();
          },
          {
            immediate: true
          }
        );
      }
    };

    // 初始化监听器
    setupWatchers();

    const docStore = useDocumentStore();

    const mapField = activeBlock.value?.dataMapping[0]?.fieldName;

    const data = computed(() => {
      const newData = docStore.widgetStore.value[activeBlock.value?.id]?.data;
      if (mapField && newData) {
        // 根据mapField对newData去重
        const seen = new Set();
        return newData.filter((item: any) => {
          const fieldValue = item[mapField];
          if (seen.has(fieldValue)) {
            return false;
          }
          seen.add(fieldValue);
          return true;
        });
      }
      return newData;
    });

    /**
     * 处理映射系列
     */
    const handeMapperSeries = () => {
      if (data.value && mapField) {
        seriesOptions.value = [];

        data.value.forEach((item: any, index: number) => {
          seriesOptions.value.push({
            id: mapField + '-' + item[mapField!],
            label: item[mapField!],
            value: index
          });
        });

        if (series.value && series.value.length) {
          const newSeries: SeriesMark[] = [];
          data.value.forEach((item: AxisField, index: number) => {
            const seriesMark = props.options.series.find(
              (sMark: SeriesMark) => sMark.id === mapField + '-' + item[mapField!]
            );
            if (seriesMark) {
              newSeries[index] = seriesMark;
            } else {
              newSeries[index] = createSeriesMark(item, index, mapField + '-' + item[mapField!]);
            }
          });

          series.value = newSeries;
        } else {
          data.value.map((item: AxisField, index: number) =>
            series.value.push(createSeriesMark(item, index, mapField + '-' + item[mapField!]))
          );
        }
        activeSeriesIndex.value = 0;
      } else {
        handleSeries();
      }
    };

    // 根据图表类型设置数据监听器
    const setupDataWatcher = () => {
      if (chartType.value === ChartType.StackBar) {
        watch(
          () => data.value,
          () => {
            handeMapperSeries();
          },
          {
            immediate: true
          }
        );
      }
    };

    // 初始化数据监听器
    setupDataWatcher();

    return {
      popupRef,
      popupShow,
      showPopup,
      seriesOptions,
      activeSeriesIndex,
      seriesName,
      activeBlock,
      chartType,
      chartGroup
    };
  }
});
