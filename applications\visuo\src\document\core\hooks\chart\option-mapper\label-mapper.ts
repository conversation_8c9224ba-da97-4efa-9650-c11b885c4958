import {
  Chart,
  ChartType,
  Effects,
  FillPaints,
  RelativePosition,
  ShowType,
  Stroke,
  type AxisFields,
  type ChartOptions,
  type TotalLabel
} from '../../../models';
import { getColorValue, getLineDashFromStroke, mapConfig } from './option-utils';

/**
 * 图形类型映射
 */
export const chartTypeMap: Record<string, string> = {
  [ChartType.Bar]: 'interval',
  [ChartType.StackBar]: 'stack-interval',
  [ChartType.Line]: 'line'
};

/**
 * 数据标签配置映射表
 * <AUTHOR>
 */
const LABEL_MAPPING = {
  label: {
    source: 'visible',
    transform: (visible: boolean, labelOptions: TotalLabel) => {
      if (!labelOptions) return false;

      return visible ? {} : false;
    },
    fallback: false
  },
  // 偏移
  'label.dx': { source: 'offsetX', default: 0 },
  'label.dy': { source: 'offsetY', default: 0 },

  // 文字样式配置
  'label.fontSize': {
    source: 'fontStyle.fontSize',
    default: 12
  },
  'label.fontFamily': {
    source: 'fontStyle.fontFamily',
    default: '默认字体'
  },
  'label.fontWeight': { source: 'fontStyle.fontWeight', default: 400 },
  'label.fill': {
    source: 'fontStyle.fillPaints',
    transform: (value: any, labelOptions: TotalLabel, options: any) => {
      return labelOptions.fontStyle.visible ? getColorValue(value) : 'transparent';
    }
  },
  'label.fillOpacity': { source: 'fontStyle.fillPaints.color.a', default: 1 },
  'label.lineHeight': { source: 'fontStyle.lineHeight', default: 0 },
  'label.textAlign': { source: 'fontStyle.alignHorizontal', default: 'start' },
  // 背景配置
  'label.background': { source: 'fillPaints.visible', transform: () => true },
  'label.backgroundFill': {
    source: 'fillPaints',
    transform: (value: FillPaints) => {
      return getColorValue(value) || 'transparent';
    }
  },
  'label.backgroundRadius': {
    source: 'radius'
  },
  'label.backgroundFillOpacity': {
    source: 'fillPaints',
    transform: (val: FillPaints) => {
      if (!val.visible) return 0;
      return val.color?.a;
    }
  },
  'label.backgroundOpacity': {
    source: 'fillPaints',
    transform: (val: FillPaints) => {
      return val.visible ? 1 : 0;
    }
  },

  // 描边
  'label.backgroundStroke': {
    source: 'stroke.fillPaints',
    transform: (fillPaints: FillPaints) => {
      return getColorValue(fillPaints);
    }
  },

  /**
   * 描边宽度
   */
  'label.backgroundLineWidth': {
    source: 'stroke.position.0'
  },

  /**
   * 描边样式
   */
  'label.backgroundLineDash': {
    source: 'stroke',
    transform: (stroke: Stroke) => getLineDashFromStroke(stroke)
  },

  // 特效
  'label.backgroundShadowColor': {
    source: 'effects.fillPaints',
    transform: (fillPaints: FillPaints, label: TotalLabel) => {
      if (!label.effects.visible) return 'transparent';
      return getColorValue(fillPaints);
    }
  },

  'label.backgroundShadowBlur': {
    source: 'effects.blur'
  },

  'label.backgroundShadowOffsetX': {
    source: 'effects.offset.x'
  },
  'label.backgroundShadowOffsetY': {
    source: 'effects.offset.y'
  },

  // 位置
  'label.position': {
    source: 'position',
    transform(value: RelativePosition) {
      if (value === RelativePosition.Center) return 'inside';
      return value;
    }
  }
} as const;

/**
 * 数据标签配置映射器
 */
export const mapLabelConfig = (
  labelConfig: TotalLabel,
  options: ChartOptions,
  yField: AxisFields,
  series: any
): Record<string, any> => {
  if (!labelConfig || !labelConfig.visible) {
    return { label: false };
  }
  const labelOptions: any = {};

  // 映射所有配置
  mapConfig(labelConfig, labelOptions, LABEL_MAPPING, options, yField, series);

  return labelOptions;
};

/**
 * 应用数据标签配置
 */
export const applyLabelConfig = (series: any, labelConfig: any, widget: Chart, label: TotalLabel, view: any) => {
  if (!series) {
    throw new Error('图表视图未初始化');
  }

  try {
    // 处理标签重叠
    view.value.labelTransform = label && label.autoHide ? [{ type: 'overlapHide' }] : undefined;
    const fieldNames = widget.yField.map((item) => item.fieldName);

    series.forEach((child: any) => {
      if (fieldNames.includes(child.value.encode.y) && chartTypeMap[widget.type].includes(child.type)) {
        child.value.labels = getLabels(labelConfig, child, label, view);
      }
    });
  } catch (error) {
    console.error('应用数据标签配置失败:', error);
    throw error;
  }
};

export const getLabels = (labelConfig: any, child: any, label: TotalLabel, view: any) => {
  if (!labelConfig) return [];

  const { showType } = label;
  const yField = child.value.encode.y;
  const data = view.value.data;

  // 显示所有值
  if (showType === ShowType.All) {
    return [
      {
        text: yField,
        ...labelConfig
      }
    ];
  }

  // 显示最大最小值
  if (showType === ShowType.MaxMin) {
    const { showMax, showMin } = label;
    if (!showMax && !showMin) return [];

    if (showMax && showMin) {
      const max = Math.max(...data.map((item: any) => item[yField]));
      const min = Math.min(...data.map((item: any) => item[yField]));

      return [
        createLabelConfig(yField, labelConfig, (d: number) => (d === max ? d : '')),
        createLabelConfig(yField, labelConfig, (d: number) => (d === min ? d : ''))
      ];
    }

    const extremeValue = showMax
      ? Math.max(...data.map((item: any) => item[yField]))
      : Math.min(...data.map((item: any) => item[yField]));

    return [createLabelConfig(yField, labelConfig, (d: any) => (d === extremeValue ? d : ''))];
  }

  // 显示首尾值
  const { showFirst, showLast } = label;
  if (!showFirst && !showLast) return [];

  if (showFirst && showLast) {
    const firstItem = data[0];
    const lastItem = data[data.length - 1];

    return [
      createLabelConfig(yField, labelConfig, (d: number, item: any) =>
        JSON.stringify(item) === JSON.stringify(firstItem) ? d : ''
      ),
      createLabelConfig(yField, labelConfig, (d: number, item: any) =>
        JSON.stringify(item) === JSON.stringify(lastItem) ? d : ''
      )
    ];
  }

  const targetItem = showFirst ? data[0] : data[data.length - 1];
  return [
    createLabelConfig(yField, labelConfig, (d: number, item: any) =>
      JSON.stringify(item) === JSON.stringify(targetItem) ? d : ''
    )
  ];
};

// 辅助函数：创建标签配置
const createLabelConfig = (text: any, baseConfig: any, formatter: any) => ({
  text,
  ...baseConfig,
  formatter
});
