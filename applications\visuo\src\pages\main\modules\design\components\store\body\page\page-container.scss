@import '../../../index';

.#{$vis-prefix}-store-page-container {
  &-header {
    @apply relative flex justify-between h-10 px-3 py-2 bg-white;

    &-search {
      @apply absolute top-0 right-0 bottom-0 left-0 flex items-center gap-2 w-full px-3;
    }

    &-input {
      @apply w-full rounded pl-1 pr-1;

      background-color: $input-bg;
    }
  }

  &-title {
    @apply flex items-center font-500;

    font-size: $primary-font-size;
  }

  &-btn {
    @include component-base;
    @apply p-0 m-0 w-6;

    font-size: $primary-font-size;

    &-group {
      @apply flex items-center gap-1;

      :deep(.q-btn) {
        .vis-icon,
        .q-icon {
          @apply text-#666 text-16px;
        }
      }
    }
  }

  &-tree_layout {
    .empty-content {
      @apply h-8 px-0 py-3 flex justify-center items-center text-#00000080;

      font-size: $primary-font-size;
    }

    > .vis-store-layer,
    > .vis-store-page {
      > .#{$vis-prefix}-store-layer-node__parent:first-child,
      > .#{$vis-prefix}-store-layer-node__child:first-child,
      > .#{$vis-prefix}-store-page-node__parent:first-child,
      > .#{$vis-prefix}-store-page-node__child:first-child {
        @apply mt-0;
      }
    }
  }

  &-tree_layout.is-dragging {
    :deep(.vis-store-layer),
    :deep(.vis-store-page) {
      .#{$vis-prefix}-store-layer-node__parent,
      .#{$vis-prefix}-store-page-node__parent {
        @apply relative;

        .vis-store-layer-collapsible.folder,
        .vis-store-page-collapsible.folder {
          @apply absolute top-0 left-0 right-0 bottom-0 m-auto h-4 w-full overflow-hidden op-0;

          .vis-store-layer,
          .vis-store-page {
            @apply pt-4;
          }
        }
      }

      .#{$vis-prefix}-store-layer-node__child {
        @apply relative;

        .vis-store-layer {
          @apply absolute top-0 left-0 right-0 bottom-0 m-auto h-4 w-full op-0;
        }
      }

      .#{$vis-prefix}-store-layer-node.selected,
      .#{$vis-prefix}-store-page-node.selected {
        @apply m-0 h-0 overflow-hidden;

        //  border-b-1px border-b-solid border-b-[#000]
      }

      .#{$vis-prefix}-store-layer-header:hover,
      .#{$vis-prefix}-store-layer-node__child:hover,
      .#{$vis-prefix}-store-page-header:hover,
      .#{$vis-prefix}-store-page-node__child:hover {
        background-color: transparent;
      }
    }

    > .vis-store-layer,
    > .vis-store-page {
      @apply pb-6;
    }
  }

  &-separator {
    position: relative;
    transition: background-color 0.2s ease;

    // cursor: ns-resize;
    cursor: row-resize;

    // 使用伪元素扩展触发区域
    &::before {
      content: '';
      position: absolute;
      z-index: 1;
      inset: -10px 0;
    }

    &:hover {
      background-color: rgb(25 118 210 / 10%);
    }

    &.is-dragging {
      background-color: rgb(25 118 210 / 20%);
    }
  }
}
