import { useFill, type FillPaints, Color, Stroke, FillType, StrokeType } from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';

/**
 * 图形面板
 * 填充+描边
 */
export default defineComponent({
  name: 'vis-graph-popup',
  props: {
    fillPaints: {
      type: Object as PropType<FillPaints>
    },
    stroke: {
      type: Object as PropType<Stroke>
    },
    /**
     * 当前所选边框位置 0 顶部  | 1 右侧 |2 底部 | 3 左侧 | 4 全部 | 5 自定义
     */
    positionIndex: {
      type: Number
    },
    noImage: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    //#region 填充
    const computedFill = computed({
      get() {
        return props.fillPaints;
      },
      set(value) {
        Object.assign({}, props.fillPaints, value);
      }
    });

    const computedStroke = computed({
      get() {
        if (!props.stroke) {
          return new Stroke();
        }
        return props.stroke;
      },
      set(value) {
        Object.assign({}, props.stroke, value);
      }
    });

    watch(
      [() => computedFill.value, () => computedStroke.value],
      ([fillVal, strokeVal]) => {
        emit('changeFillPaints', fillVal);
        emit('changeStroke', strokeVal);
      },
      {
        deep: true
      }
    );

    //#endregion 填充
    return {
      computedFill,
      computedStroke,
      StrokeType
    };
  }
});
