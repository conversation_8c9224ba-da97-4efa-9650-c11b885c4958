import { snapdom, type SnapOptions } from '@zumer/snapdom';
import html2canvas from 'html2canvas';
import * as HtmlToImage from 'html-to-image';
/**
 * 生成缩略图配置
 * */
export interface ThumbnailOptions {
  /** 缩略图宽 */
  width: number;
  /** 缩略图高 */
  height: number;
  /** 缩放比例 */
  scale?: number;
  /** 图片质量（0-1） */
  quality?: number;
}

/** 生成缩略图 原DOM */
export const thumbnailDom = async (element: HTMLElement, options: ThumbnailOptions): Promise<HTMLImageElement> => {
  const { width, height, scale = 1, quality = 0.95 } = options;
  // 元素是否存在
  if (!element) {
    throw new Error('Error: element is required!');
  }
  // 截图
  const snapOptions: SnapOptions = {
    width,
    height,
    quality,
    scale,
    type: 'png',
    backgroundColor: '#ffffff'
  };
  const snapImage: HTMLImageElement = await snapdom.toPng(element, snapOptions);

  // 设置缩略图宽高
  snapImage.width = width;
  snapImage.height = height;
  // 返回截图结果
  return snapImage;
};

/***
 * 生成缩略图
 * @param element 要生成缩略图的DOM元素
 * @param options 缩略图配置
 * @returns Promise<HTMLImageElement> 生成的缩略图
 * */
export const thumbnail = async (element: HTMLElement, options: ThumbnailOptions): Promise<HTMLImageElement> => {
  // 提取参数
  const { width, height, scale = 1, quality = 0.95 } = options;

  // 元素是否存在
  if (!element) {
    throw new Error('Error: element is required!');
  }

  // 确保资源加载完毕
  await ensureResourcesLoaded(element);

  // 预处理：克隆原始元素
  const clone = element.cloneNode(true) as HTMLElement;

  // 放置到可视区域以外
  clone.style.width = `${width / scale}px`;
  clone.style.height = `${height / scale}px`;
  clone.style.position = 'absolute';
  // clone.style.top = '0px';
  // clone.style.left = '0px';
  // clone.style.zIndex = '999999999 !important';
  clone.style.top = '-9999px';
  clone.style.left = '-9999px';
  clone.style.zIndex = '-1';
  document.body.appendChild(clone);

  // 等待一帧确保渲染
  await waitForFrame();

  // 预处理：处理克隆元素中特殊元素
  await preprocessCloned(clone, element);

  // 等待一帧确保渲染
  await waitForFrame();

  // 截图
  let snapImage: HTMLImageElement;
  const snapOptions: SnapOptions = {
    width,
    height,
    quality,
    scale
  };
  try {
    snapImage = await snapdom.toPng(clone, snapOptions);
  } catch (error) {
    // 克隆元素截图失败，使用原始元素截图
    snapImage = await snapdom.toPng(element, snapOptions);
  }

  // 移除临时元素

  document.body.removeChild(clone);

  // 设置缩略图宽高
  snapImage.width = width;
  snapImage.height = height;
  // 返回截图结果
  return snapImage;
};

/**
 * 确保所有资源（视频，图片）加载完毕
 * @param element 要确保加载完毕的DOM元素
 * @returns Promise<void> 加载完毕
 *  */
export const ensureResourcesLoaded = async (element: HTMLElement): Promise<void> => {
  // 提取所有图片和视频
  const images = element.querySelectorAll('img');
  const videos = element.querySelectorAll('video');

  // 等待所有图片加载完成
  const imageLoadPromises = Array.from(images).map((img) => {
    if (img.complete) return Promise.resolve();
    return new Promise<void>((resolve, reject) => {
      img.onload = () => resolve();
      img.onerror = () => {
        console.warn(`image loaded error: ${img.src}`);
        resolve(); // 继续执行，不中断流程
      };
      // 设置超时处理
      setTimeout(() => resolve(), 5000);
    });
  });

  // 等待所有视频加载第一帧
  const videoLoadPromises = Array.from(videos).map((video) => {
    return new Promise<void>((resolve) => {
      if (video.readyState >= 1) {
        resolve();
      } else {
        video.addEventListener('loadeddata', () => resolve(), { once: true });
        video.addEventListener('error', () => {
          console.warn(`video loaded error: ${video.src}`);
          resolve(); // 继续执行，不中断流程
        });
        // 设置超时处理
        setTimeout(() => resolve(), 5000);
      }
    });
  });
  
  // 等待所有资源加载完成
  await Promise.all([...imageLoadPromises, ...videoLoadPromises]);
};

/**
 * 处理克隆元素中特殊元素，例如：canvas/video
 * @param clone 克隆元素
 * @param element 原始元素
 * @returns Promise<void> 处理完成
 * */
export const preprocessCloned = async (clone: HTMLElement, element: HTMLElement): Promise<void> => {
  // 处理canvas元素
  const canvasList = element.querySelectorAll('canvas');
  const clonedCanvasList = clone.querySelectorAll('canvas');

  clonedCanvasList.forEach((canvas, index) => {
    const originalCanvas = canvasList[index];
    const img = document.createElement('img');
    try {
      img.src = originalCanvas.toDataURL('image/png');
      canvas.replaceWith(img);
    } catch (error) {
      console.warn(`canvas error: ${error}`);
    }
  });

  // 处理video元素
  const videoList = element.querySelectorAll('video');
  const clonedVideoList = clone.querySelectorAll('video');

  clonedVideoList.forEach((video, index) => {
    const originalVideo = videoList[index];
    const img = document.createElement('img');
    try {
      const canvas = document.createElement('canvas');
      canvas.width = originalVideo.videoWidth;
      canvas.height = originalVideo.videoHeight;
      const ctx = canvas.getContext('2d');
      if (ctx && originalVideo.readyState >= 1) {
        ctx.drawImage(originalVideo, 0, 0, canvas.width, canvas.height);
        img.src = canvas.toDataURL('image/png');
        video.replaceWith(img);
      } else {
        console.warn(`video loaded error: ${originalVideo.src}`);
      }
    } catch (error) {
      console.warn(`video error: ${error}`);
    }
  });

  // 处理svg元素
  const svgElements = element.querySelectorAll('svg');
  const clonedSvgElements = clone.querySelectorAll('svg');
  // debugger;
  clonedSvgElements.forEach(async (svg, index) => {
    try {
      const originalSvg = svgElements[index];
      let svgHrefDom = originalSvg.querySelector('svg use');
      let hrefStr = svgHrefDom?.getAttribute('xlink:href');
      if (!svgHrefDom) {
        // svgHrefDom = originalSvg.querySelector('image');
        // hrefStr = svgHrefDom?.getAttribute('href');

        svgHrefDom = originalSvg.querySelector('image');
        if (svgHrefDom) {
          const dataUrl = await HtmlToImage.toSvg(originalSvg);
          hrefStr = dataUrl;
          // HtmlToImage.toPng(originalSvg).then((dataUrl) => {
          //   hrefStr = dataUrl;
          //   const img = document.createElement('img');
          //   img.src = hrefStr;
          //   img.width = svg.clientWidth;
          //   img.height = svg.clientHeight;
          //   svg.replaceWith(img);
          // });
          // thumbnailCanvas(originalSvg.clientWidth, originalSvg.clientHeight, originalSvg, 1).then((img) => {
          //   hrefStr = img.src;
          // });
        }
      }
      if (!svgHrefDom || !hrefStr) {
        // 直接跳出
        return;
      }

      const img = document.createElement('img');
      img.src = hrefStr;
      img.width = svg.clientWidth;
      img.height = svg.clientHeight;
      svg.replaceWith(img);
    } catch (error) {
      console.warn('svg handle error:', error);
    }
  });
};

/**
 * 等待一帧确保元素渲染
 * */
export const waitForFrame = async (): Promise<void> => {
  return new Promise((resolve) => requestAnimationFrame(() => resolve()));
};

/**
 * 生成缩略图 html2canvas
 * @param coverWidth 封面宽度
 * @param coverHeight 封面高度
 * @param dom 要生成缩略图的DOM元素
 * @param scale 缩放比例
 * @returns Promise<HTMLImageElement> 生成的缩略图
 * */
export const thumbnailCanvas = async (
  coverWidth: number,
  coverHeight: number,
  dom: any,
  scale: number = 1
): Promise<HTMLImageElement> => {
  const canvas = await html2canvas(dom, {
    useCORS: true,
    allowTaint: true,
    logging: false,
    foreignObjectRendering: true,
    scale: scale,
    backgroundColor: 'rgba(255, 255, 255, 0)'
  });
  const width = canvas.width;
  const height = canvas.height;
  const x = Math.max(0, (width - coverWidth) / 2);
  const y = Math.max(0, (height - coverHeight) / 2);
  const ctx = canvas.getContext('2d');
  if (ctx) {
    const croppedCanvas = document.createElement('canvas');
    croppedCanvas.width = coverWidth;
    croppedCanvas.height = coverHeight;
    const croppedCtx = croppedCanvas.getContext('2d');
    if (croppedCtx) {
      croppedCtx.drawImage(canvas, x, y, coverWidth, coverHeight, 0, 0, coverWidth, coverHeight);
    }
    const image = document.createElement('img');
    image.src = croppedCanvas.toDataURL('image/png');
    return image;
  }
  return document.createElement('img');
};
