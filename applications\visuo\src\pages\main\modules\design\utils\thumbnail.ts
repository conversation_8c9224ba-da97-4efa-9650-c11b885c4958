import { snapdom, type SnapOptions } from '@zumer/snapdom';
import * as HtmlToImage from 'html-to-image';
/**
 * 生成缩略图配置
 * */
export interface ThumbnailOptions {
  /** 缩略图宽 */
  width: number;
  /** 缩略图高 */
  height: number;
  /** 缩放比例 */
  scale?: number;
  /** 图片质量（0-1） */
  quality?: number;
}

/***
 * 生成缩略图
 * @param element 要生成缩略图的DOM元素
 * @param options 缩略图配置
 * @returns Promise<HTMLImageElement> 生成的缩略图
 * */
export const thumbnail = async (element: HTMLElement, options: ThumbnailOptions): Promise<HTMLImageElement> => {
  // 提取参数
  const { width, height, scale = 1, quality = 0.95 } = options;

  // 元素是否存在
  if (!element) {
    throw new Error('Error: element is required!');
  }

  // 确保资源加载完毕
  await ensureResourcesLoaded(element);

  // 预处理：克隆原始元素
  const clone = element.cloneNode(true) as HTMLElement;

  // 放置到可视区域以外
  clone.style.width = `${width / scale}px`;
  clone.style.height = `${height / scale}px`;
  clone.style.position = 'absolute';
  clone.style.top = '-9999px';
  clone.style.left = '-9999px';
  clone.style.zIndex = '-1';
  document.body.appendChild(clone);

  // 等待一帧确保渲染
  await waitForFrame();

  // 预处理：处理克隆元素中特殊元素
  await preprocessCloned(clone, element);

  // 等待一帧确保渲染
  await waitForFrame();

  // 截图
  let snapImage: HTMLImageElement;
  const snapOptions: SnapOptions = {
    width,
    height,
    quality,
    scale
  };
  try {
    snapImage = await snapdom.toPng(clone, snapOptions);
  } catch (error) {
    // 克隆元素截图失败，使用原始元素截图
    snapImage = await snapdom.toPng(element, snapOptions);
  }

  // 移除临时元素
  document.body.removeChild(clone);
  // 设置缩略图宽高
  snapImage.width = width;
  snapImage.height = height;
  // 返回截图结果
  return snapImage;
};

/**
 * 确保所有资源（视频，图片，SVG）加载完毕
 * @param element 要确保加载完毕的DOM元素
 * @returns Promise<void> 加载完毕
 *  */
export const ensureResourcesLoaded = async (element: HTMLElement): Promise<void> => {
  // 提取所有图片、视频和SVG
  const images = element.querySelectorAll('img');
  const videos = element.querySelectorAll('video');
  const svgs = element.querySelectorAll('svg');

  // 等待所有图片加载完成
  const imageLoadPromises = Array.from(images).map((img) => {
    if (img.complete) return Promise.resolve();
    return new Promise<void>((resolve) => {
      img.onload = () => resolve();
      img.onerror = () => {
        console.warn(`image loaded error: ${img.src}`);
        resolve(); // 继续执行，不中断流程
      };
      // 设置超时处理
      setTimeout(() => resolve(), 5000);
    });
  });

  // 等待所有视频加载第一帧
  const videoLoadPromises = Array.from(videos).map((video) => {
    return new Promise<void>((resolve) => {
      if (video.readyState >= 1) {
        resolve();
      } else {
        video.addEventListener('loadeddata', () => resolve(), { once: true });
        video.addEventListener('error', () => {
          console.warn(`video loaded error: ${video.src}`);
          resolve(); // 继续执行，不中断流程
        });
        // 设置超时处理
        setTimeout(() => resolve(), 5000);
      }
    });
  });

  // 等待所有SVG资源加载完成
  const svgLoadPromises = Array.from(svgs).map((svg) => {
    return new Promise<void>((resolve) => {
      // 检查SVG中的use元素是否引用外部资源
      const useElements = svg.querySelectorAll('use');
      const imageElements = svg.querySelectorAll('image');

      if (useElements.length === 0 && imageElements.length === 0) {
        // 没有外部引用，直接resolve
        resolve();
        return;
      }

      // 等待SVG渲染完成
      requestAnimationFrame(() => setTimeout(() => resolve(), 100));
    });
  });

  // 等待所有资源加载完成
  await Promise.all([...imageLoadPromises, ...videoLoadPromises, ...svgLoadPromises]);
};

/**
 * 处理克隆元素中特殊元素，例如：canvas/video
 * @param clone 克隆元素
 * @param element 原始元素
 * @returns Promise<void> 处理完成
 * */
export const preprocessCloned = async (clone: HTMLElement, element: HTMLElement): Promise<void> => {
  // 处理canvas元素
  const canvasList = element.querySelectorAll('canvas');
  const clonedCanvasList = clone.querySelectorAll('canvas');
  clonedCanvasList.forEach((canvas, index) => {
    const originalCanvas = canvasList[index];
    const img = document.createElement('img');
    try {
      img.src = originalCanvas.toDataURL('image/png');
      canvas.replaceWith(img);
    } catch (error) {
      console.warn(`canvas error: ${error}`);
    }
  });

  // 处理video元素
  const videoList = element.querySelectorAll('video');
  const clonedVideoList = clone.querySelectorAll('video');
  clonedVideoList.forEach((video, index) => {
    const originalVideo = videoList[index];
    const img = document.createElement('img');
    try {
      const canvas = document.createElement('canvas');
      canvas.width = originalVideo.videoWidth;
      canvas.height = originalVideo.videoHeight;
      const ctx = canvas.getContext('2d');
      if (ctx && originalVideo.readyState >= 1) {
        ctx.drawImage(originalVideo, 0, 0, canvas.width, canvas.height);
        img.src = canvas.toDataURL('image/png');
        video.replaceWith(img);
      } else {
        console.warn(`video loaded error: ${originalVideo.src}`);
      }
    } catch (error) {
      console.warn(`video error: ${error}`);
    }
  });

  // 处理svg元素
  const svgElements = element.querySelectorAll('svg');
  const clonedSvgElements = clone.querySelectorAll('svg');
  const svgProcessPromises = Array.from(clonedSvgElements).map(async (svg, index) => {
    try {
      const originalSvg = svgElements[index];
      // 处理use/image
      let hrefDom = originalSvg.querySelector('use');
      let hrefStr = hrefDom?.getAttribute('xlink:href');

      if (!hrefDom) {
        hrefDom = originalSvg.querySelector('image');
        hrefDom && (hrefStr = await HtmlToImage.toSvg(originalSvg as unknown as HTMLElement));
      }

      if (!hrefDom || !hrefStr) return;

      const img = document.createElement('img');
      img.src = hrefStr;
      img.width = svg.clientWidth || originalSvg.clientWidth;
      img.height = svg.clientHeight || originalSvg.clientHeight;

      // 确保图片加载完成后再替换
      await new Promise<void>((resolve) => {
        img.onload = () => resolve();
        img.onerror = () => {
          console.warn(`SVG converted image load error: ${hrefStr}`);
          resolve(); // 继续执行，不中断流程
        };
        // 设置超时处理
        setTimeout(() => resolve(), 3000);
      });

      svg.replaceWith(img);
    } catch (error) {
      console.warn('svg handle error:', error);
    }
  });

  // 使用Promise.all等待所有SVG处理完成
  await Promise.all(svgProcessPromises);

  // 处理--border-width,--border-image,--border样式不支持截图问题
  // '--box-shadow': 'initial',
  //       '--background': 'initial',
  //       '--border': 'initial',
  //       '--border-width': 'initial',
  //       '--border-image': 'initial'
  // 查找所有包含--border-width,--border样式的.vis-graph元素
  debugger;
  const borderGraphs = clone.querySelectorAll('.vis-graph[style*="--border-width"],.vis-graph[style*="--border"]');
  // 过滤掉--border-width,--border为initial的元素
  const filteredBorderGraphs = Array.from(borderGraphs).filter((graph) => {
    if (graph instanceof HTMLElement) {
      const style = getComputedStyle(graph);
      const borderWidth = style.getPropertyValue('--border-width') || 'initial';
      const border = style.getPropertyValue('--border') || 'initial';
      return borderWidth !== 'initial' && border !== 'initial';
    }
    return false;
  });
  filteredBorderGraphs.forEach((graph) => {
    if (graph instanceof HTMLElement) {
      console.log(graph, 'graph - style');
      const style = getComputedStyle(graph);
      graph.style.borderWidth = style.getPropertyValue('--border-width');
      graph.style.border = style.getPropertyValue('--border');
    }
  });
};

/**
 * 等待一帧确保元素渲染
 * */
export const waitForFrame = async (): Promise<void> => {
  return new Promise((resolve) => requestAnimationFrame(() => resolve()));
};
