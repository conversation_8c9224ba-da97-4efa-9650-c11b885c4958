export const useNumber = () => {
  /**
   * 根据精度获取值
   * @param val
   * @returns
   */
  const getValue = (value: number | string, precision?: number | string) => {
    // const value = val !== undefined ? val : props.modelValue;
    if (typeof value === 'string' && !isNumeric(value)) return `${value}`.replace('%', '');

    const data = typeof value === 'string' ? Number(value.replace('%', '')) : value;

    if (precision === undefined || Number(precision) === 0) return customRound(data);
    return data.toFixed(Number(precision));
  };

  /**
   * 四舍五入取整
   * @param num
   * @returns
   */
  const customRound = (num: number) => {
    // 分离整数和小数部分
    const integer = Math.floor(num);
    const decimal = num - integer;

    // 如果小数部分 < 0.5 或等于 0.5 但整数部分是偶数（可选）
    if (decimal < 0.5) {
      return integer;
    } else {
      return integer + 1;
    }
  };

  /**
   * 判断是不是字符串数字
   * @param str
   * @returns
   */
  const isNumeric = (str: string) => {
    return !isNaN(Number(str)) && !isNaN(parseFloat(str));
  };

  return {
    getValue,
    customRound,
    isNumeric
  };
};
