<template>
  <div class="vis-graph-popup">
    <template v-if="fillPaints">
      <div class="vis-graph-popup-padding">
        <vis-color-selector v-model="computedFill" title="填充" />
        <q-separator />

        <vis-custom-color class="mt-3" v-model="computedFill" colorType="fill"></vis-custom-color>
      </div>
    </template>
    <!-- 边框 -->
    <template v-if="stroke">
      <q-separator />
      <div class="vis-graph-popup-padding">
        <vis-stroke-style v-model="computedStroke">
          <template #title>
            <slot name="strokeStyleTitle"></slot>
          </template>
        </vis-stroke-style>
        <template v-if="computedStroke.style !== StrokeType.None">
          <vis-stroke-width v-model="stroke.position" :positionIndex="positionIndex">
            <template #title>
              <slot name="strokeWidthTitle"></slot>
            </template>
          </vis-stroke-width>
          <q-separator />
          <vis-color-selector class="pt-3" v-model="stroke.fillPaints" title="边框颜色">
            <template #title>
              <slot name="strokeFillTitle"></slot>
            </template>
          </vis-color-selector>
          <q-separator />
          <vis-custom-color class="pt-3" v-model="stroke.fillPaints" colorType="stroke" noImage></vis-custom-color>
        </template>
      </div>
    </template>
  </div>
</template>

<script src="./index.ts" lang="ts"></script>
<style src="./index.scss" lang="scss"></style>
