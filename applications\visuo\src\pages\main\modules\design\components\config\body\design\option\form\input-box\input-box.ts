import { FormFix, Label, type InputBoxOptions, FormRuleType, FormRule } from '@vis/document-core';
import { computed, defineComponent, type PropType } from 'vue';
import { useWidget } from '../../../../../../../hooks';
import { VisFormLabel, VisFormFix, VisFormRules, VisFormBoxStyle } from '../common';

/**
 * 文本输入组件属性面板
 * @authot guohuizheng
 */
export default defineComponent({
  name: 'vis-config-input-box-option',
  components: {
    VisFormLabel,
    VisFormFix,
    VisFormRules,
    VisFormBoxStyle
  },
  props: {
    options: {
      type: Object as PropType<InputBoxOptions>,
      required: true
    }
  },
  setup(props) {
    const { handleManage } = useWidget();
    const computedOptions = computed(() => props.options);

    // 前后缀(按照前缀图标、前缀文本、后缀图标、后缀文本，依次添加)
    const fixList = [true, true, true, true];

    // 可添加的校验规则列表
    const ruleList = [
      FormRuleType.Required,
      FormRuleType.Email,
      FormRuleType.Url,
      FormRuleType.Tel,
      FormRuleType.Length,
      FormRuleType.Pattern
    ];

    const labelManage = () => {
      handleManage(computedOptions.value, 'label');

      if (computedOptions.value.label) {
        computedOptions.value.label.text = '文本输入';
      }
    };

    return {
      computedOptions,

      fixList,

      ruleList,
      labelManage
    };
  }
});
