{"name": "hetu-app-visuo", "description": "太极河图可视化设计应用", "version": "1.0.0", "private": true, "scripts": {"serve": "node scripts/service.js --serve --apps", "build": "node scripts/service.js --build --apps", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:stylelint": "stylelint \"./**/*.{vue,css,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "type-check": "vue-tsc --noEmit -p tsconfig.json --composite false", "commit": "git-cz", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "dependencies": {"@antv/g2": "^5.4.0", "@hetu/acl": "workspace:*", "@hetu/auth": "workspace:*", "@hetu/core": "workspace:*", "@hetu/http": "workspace:*", "@hetu/metadata-shared": "workspace:*", "@hetu/platform-app": "workspace:*", "@hetu/platform-boot": "workspace:*", "@hetu/platform-cooperation": "workspace:*", "@hetu/platform-shared": "workspace:*", "@hetu/theme": "workspace:*", "@hetu/util": "workspace:*", "@syncedstore/core": "^0.6.0", "@y/websocket-server": "^0.1.1", "@zumer/snapdom": "^1.9.5", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "infinite-viewer": "~0.29.1", "monaco-editor": "^0.17.0", "selecto": "^1.26.3", "vue3-guides": "^0.12.2", "vue3-infinite-viewer": "^0.17.1", "vue3-moveable": "^0.28.0", "vue3-selecto": "^1.12.3", "vuedraggable": "^4.1.0", "y-indexeddb": "^9.0.12", "y-websocket": "^3.0.0", "yjs": "^13.6.26"}, "devDependencies": {"@hetu/cli": "workspace:*"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "@hetu/cli/commitizen.js"}}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"subject-case": [0, "always", "upper-case"]}}}