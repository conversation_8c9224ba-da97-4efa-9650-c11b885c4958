<template>
  <div class="vis-number-container relative">
    <q-input
      ref="numberInput"
      class="vis-number flex-1 rounded-borders overflow-hidden vis-field--mini px-2 no-spin"
      :class="[{ 'no-drag': readonly }, { '!pr-0': percentage }]"
      v-model="numberValue"
      :type="placeStr ? 'text' : 'number'"
      borderless
      dense
      @focus="handleFocus"
      @keypress.enter="handleBlur"
      @blur="handleBlur"
      @input="updateSuffixPosition"
      v-stop-change
      :step="step"
      :disable="disabled"
      :readonly="readonly"
    >
      <!-- 前置插槽 -->
      <template v-if="$slots.icon || icon" #prepend>
        <slot name="icon">
          <template v-if="icon">
            <ht-icon v-if="isIconFont" :name="icon" class="vis-icon drag-icon" />
            <q-icon v-else :name="icon" class="vis-icon drag-icon" />
          </template>
        </slot>
      </template>

      <!-- 后置插槽 -->
      <template v-if="$slots.append || percentage" #append>
        <slot name="append"></slot>
        <q-btn v-if="percentage">
          <q-icon name="keyboard_arrow_down" class="!text-xs" />
          <q-menu v-model="showMenu" style="width: 100px" class="vis-menu" dense>
            <q-list dense>
              <q-item clickable v-close-popup @click="changeType('number')" :active="!isPercentage">
                <q-item-section>数值</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="changeType('percentage')" :active="isPercentage">
                <q-item-section>百分比</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </template>

      <!-- 在输入框之前的插槽 -->
      <template v-if="$slots.before" #before>
        <slot name="before"></slot>
      </template>

      <!-- 在输入框之后的插槽 -->
      <template v-if="$slots.after" #after>
        <slot name="after"></slot>
      </template>

      <!-- 提示 -->
      <q-tooltip v-if="tooltip" :offset="[0, 4]">{{ tooltip }}</q-tooltip>
    </q-input>

    <!-- 跟随数字的后缀符号 -->
    <span
      v-if="suffixValue && showSuffix"
      ref="suffixElement"
      class="vis-number-suffix absolute pointer-events-none"
      :style="suffixStyle"
    >
      {{ suffixValue }}
    </span>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
