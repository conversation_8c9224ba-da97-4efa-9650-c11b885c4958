<template>
  <vis-text :option="labelOptions.fontStyle" mode="base" fillLabel="文字" mini isColor :showEyes="false"> </vis-text>
  <div class="vis-form-inline">
    <div class="vis-form-inline__content--minus-32 !items-start">
      <div class="vis-form-field">
        <div class="vis-form-field__label">位置</div>
        <div class="vis-form-field__content">
          <div class="vis-label-align">
            <div></div>

            <div
              :class="{
                hand: true,
                active: labelOptions.position === RelativePosition.Top
              }"
              @click="labelOptions.position = RelativePosition.Top"
            >
              <div class="dot"></div>
              <q-tooltip> 顶部 </q-tooltip>
            </div>

            <div></div>

            <div
              :class="{
                hand: true,
                active: labelOptions.position === RelativePosition.Left
              }"
              @click="labelOptions.position = RelativePosition.Left"
            >
              <div class="dot"></div>
              <q-tooltip> 左侧 </q-tooltip>
            </div>

            <div
              :class="{
                hand: true,
                active: labelOptions.position === RelativePosition.Center
              }"
              @click="labelOptions.position = RelativePosition.Center"
            >
              <div class="dot"></div>
              <q-tooltip> 居中 </q-tooltip>
            </div>
            <div
              :class="{
                hand: true,
                active: labelOptions.position === RelativePosition.Right
              }"
              @click="labelOptions.position = RelativePosition.Right"
            >
              <div class="dot"></div>
              <q-tooltip> 右侧 </q-tooltip>
            </div>

            <div></div>

            <div
              :class="{
                hand: true,
                active: labelOptions.position === RelativePosition.Bottom
              }"
              @click="labelOptions.position = RelativePosition.Bottom"
            >
              <div class="dot"></div>
              <q-tooltip> 底部 </q-tooltip>
            </div>

            <div></div>
          </div>
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">偏移</div>
        <div class="vis-form-field__content column">
          <vis-number v-model="labelOptions.offsetX" icon="vis-letter-x" precision="0" />
          <vis-number v-model="labelOptions.offsetY" icon="vis-letter-y" precision="0" />
        </div>
      </div>
    </div>
  </div>
  <div class="vis-form-inline" :class="{ 'vis-invisible': !labelOptions.fillPaints.visible }">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__label">背景</div>
        <div class="vis-form-field__content">
          <vis-fill
            v-model="labelOptions.fillPaints"
            :stroke="labelOptions.stroke"
            :effects="labelOptions.effects"
            :minusWidth="0"
            base
            :showEyes="false"
            noImage
          ></vis-fill>
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">圆角</div>
        <div class="vis-form-field__content">
          <vis-number
            v-model="radius"
            precision="0"
            icon="hticon-vis-radius"
            @update:model-value="radiusChange"
            :min="0"
          />
        </div>
      </div>
    </div>
    <q-btn class="btn-field" flat dense @click="handleVisible">
      <ht-icon class="vis-icon" :name="labelOptions.fillPaints.visible ? 'vis-eye-o' : 'vis-eye-c'" />
    </q-btn>
  </div>
</template>
<script src="./label.ts" lang="ts"></script>
<style src="./label.scss" lang="scss"></style>
