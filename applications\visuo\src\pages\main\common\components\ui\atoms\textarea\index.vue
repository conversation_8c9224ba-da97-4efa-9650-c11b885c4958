<template>
  <div class="vis-textarea">
    <q-input
      v-model="content"
      type="textarea"
      class="vis-input rounded-borders max-h-50 overflow-auto px-2 hide-scrollbar ht-hoverable"
      autogrow
      borderless
    >
      <q-btn
        class="absolute top-0 right--2 ht-hover--show"
        flat
        dense
        @click.stop="showPopup"
        style="background: rgb(246, 247, 250)"
      >
        <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
      </q-btn>
    </q-input>
    <vis-popup ref="popupRef" title="文本内容" :target="false" width="400px" height="640px" @hide="popupShow = false">
      <q-input
        v-model="content"
        type="textarea"
        class="vis-input rounded-borders overflow-auto px-2 hide-scrollbar"
        input-style="height: 570px; resize: none; padding-top: 0;"
        borderless
      />
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
