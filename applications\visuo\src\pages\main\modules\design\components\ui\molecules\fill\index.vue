<template>
  <div class="vis-fill h-full" :class="mini ? '' : 'w-full'">
    <div v-if="mini" class="vis-fill--mini">
      <q-btn
        v-if="btnType === BtnType.Color"
        class="vis-field--mini"
        @click.stop="showPopup"
        :class="{ active: popupShow }"
      >
        <q-icon
          class="cursor-pointer vis-fill__icon"
          :class="isEmptyImage ? 'hticon-vis-image' : 'hticon-vis-rect'"
          style="font-size: 20px !important"
          :style="iconStyle"
        ></q-icon>
      </q-btn>

      <q-btn
        v-else-if="btnType === BtnType.Text"
        class="vis-btn"
        :class="{ active: popupShow }"
        @click.stop="showPopup"
      >
        <span :style="textColor" class="text-xs">A</span>
      </q-btn>

      <q-btn
        v-else
        class="vis-btn"
        @click.stop="showPopup"
        :class="{ active: popupShow }"
        style="width: 16px; height: 16px"
      >
        <q-icon class="hticon-vis-add" size="14px"></q-icon>
      </q-btn>
    </div>
    <div v-else class="vis-form-inline">
      <div :class="`vis-form-inline__content--minus-${minusWidth}`">
        <div class="flex vis-fill__content" :class="{ 'vis-invisible': !visible }">
          <!-- 颜色按钮 -->
          <slot v-if="$slots.color" name="color"></slot>
          <q-btn v-else class="vis-field--mini" @click.stop="showPopup" :class="{ active: popupShow }">
            <q-icon
              v-if="computedModel.color"
              class="cursor-pointer vis-fill__icon"
              :class="isEmptyImage ? 'hticon-vis-image' : 'hticon-vis-rect'"
              :style="iconStyle"
            ></q-icon>
            <div v-else style="display: flex; align-items: center; width: 16px; height: 16px; background-color: #fff">
              <ht-icon style="color: red" class="vis-icon" name="vis-none" />
            </div>
          </q-btn>

          <!-- 输入框 -->
          <div class="flex-1 row items-center pl-1" v-if="!base">
            <template v-if="isSolid">
              <q-input
                ref="colorRef"
                borderless
                dense
                :model-value="hexColor"
                @update:model-value="handleUpdateColor"
                @focus="focusColor"
                @keypress.enter="updateColor"
                @blur="updateColor"
                class="vis-field--mini w-[calc(100%-41px)]"
              />
              <q-separator vertical inset class="!m-0" />
              <q-input
                borderless
                dense
                v-model="alpha"
                @focus="focusAlpha"
                @keypress.enter="updateAlpha"
                @blur="updateAlpha"
                class="vis-field--mini w-32px mx-1"
              />
            </template>
            <template v-else-if="isGradient">
              <q-input
                borderless
                dense
                :modelValue="fillTypeName"
                readonly
                class="vis-field--mini w-[calc(100%-41px)]"
              />
              <q-separator vertical inset class="!m-0" />
              <q-input
                ref="alphaRef"
                borderless
                dense
                v-model="alpha"
                @focus="focusAlpha"
                @keypress.enter="updateAlpha"
                @blur="updateAlpha"
                class="vis-field--mini w-32px mx-1"
              />
            </template>

            <q-input v-else borderless :modelValue="fillTypeName" readonly class="vis-field--mini col-12" />
          </div>
          <div class="flex-1 row items-center" v-else>
            <q-input
              borderless
              dense
              v-model="alpha"
              @focus="focusAlpha"
              @keypress.enter="updateAlpha"
              @blur="updateAlpha"
              class="vis-field--mini mx-1"
            />
          </div>
        </div>
      </div>

      <!-- 显隐按钮 -->
      <q-btn v-if="showEyes" class="vis-field--mini btn-field" flat @click="handleVisible">
        <ht-icon class="vis-icon" :name="visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
      </q-btn>
    </div>

    <!-- 弹窗 -->
    <vis-popup ref="popupRef" :title="popTitle || popupTitle" @before-hide="handleHide" :target="false">
      <div v-if="!stroke && !effects">
        <div v-if="groupShow" class="vis-fill__types q-mb-sm vis-button-group flex-1 flex rounded-borders">
          <template v-for="type in typeOptions" :key="type.value">
            <q-btn
              v-if="showFillType(type.value)"
              flat
              dense
              @click="colorTypeUpdate(type.value)"
              class="flex-1"
              :class="{ 'vis-btn-active': colorType === type.value }"
            >
              <img
                class="img-icon"
                :src="`./static-next/svg/fill/fill-${type.icon}.svg`"
                :class="{ grayscale: colorType !== type.value }"
              />
              <q-tooltip :offset="[0, 4]"> {{ type.tip }} </q-tooltip>
            </q-btn>
          </template>
        </div>

        <!-- 纯色 -->
        <vis-fill-color v-if="isSolid" ref="solidRef" v-model="computedModel.color" />

        <!-- 渐变 -->
        <vis-fill-gradient
          v-if="isGradient"
          ref="gradientRef"
          v-model="computedModel"
          @update:modelValue="handleUpdate"
        />

        <!-- 图片 -->
        <vis-fill-image v-if="isImage" v-model="computedModel" @update:modelValue="handleUpdate" />
      </div>
      <!-- 填充 -->
      <template #title v-if="effects">
        <q-tabs
          v-model="tab"
          dense
          active-color="black"
          active-bg-color="grey-2"
          indicator-color="transparent"
          align="left"
          narrow-indicator
          class="vis-tabs"
        >
          <q-tab name="graph" :label="popTitle || '图形'" />
          <q-tab v-if="effects" name="effects" label="特效" />
        </q-tabs>
      </template>
      <template #content v-if="stroke">
        <vis-graph-popup
          v-if="tab === 'graph'"
          v-model:fillPaints="computedModel"
          v-model:stroke="stroke"
          :positionIndex="positionIndex"
          :noImage="noImage"
        ></vis-graph-popup>
        <vis-effects-popup class="pa-3" v-if="tab === 'effects'" :options="effects"></vis-effects-popup>
      </template>
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
