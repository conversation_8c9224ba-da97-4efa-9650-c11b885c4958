@import '../../../index';

.#{$vis-prefix}-config-page {
  &-thumbnail {
    @apply flex items-center justify-center w-216px h-121px bg-#F4F4F6 rounded overflow-hidden;

    box-shadow: 0 0 2px #0000001f;

    &__img {
      @apply w-full h-full object-cover;

      object-position: center center;
    object-fit: cover;
    }
  }

  &-theme {
    @apply mb-2;

    &-title {
      @apply text-2.5 font-400 c-#666 lh-3.5 mb-2;
    }

    &-body {
      @apply flex flex-wrap;

      &-color {
        @apply flex items-center justify-center w-4 h-4 mr-1px cursor-pointer;
      }

      &-palette {
        @apply flex items-center justify-center w-3.5 h-3.5 rounded-4 cursor-pointer;

        &.selected {
          outline: 1px solid $select-active-color;
        }
      }

      &-color,
      &-palette {
        &:hover {
          box-shadow: $box-shadow;
        }
      }
    }
  }
}
