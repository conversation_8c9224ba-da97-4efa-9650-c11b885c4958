import type { Axis } from '.';
import { WidgetBlock } from '../widget-block';
import type { BarOptions } from './bar/bar';
import { TotalLabel } from './label';
import { Legend } from './legend';
import { Mark } from './mark';
import type { LineOptions } from './line/line';

/**
 * 图表
 * <AUTHOR>
 */
export class Chart extends WidgetBlock {
  /**
   * 图表配置
   */
  options: ChartOptions = new ChartOptions();
}

/**
 * 图表配置
 * <AUTHOR>
 */
export class ChartOptions {
  /** 图表图例 */
  legend: Legend = new Legend();
  /** 图表提示 */

  /** 图表工具 */

  /** 图表X轴 */
  xAxis?: Axis;

  /** 图表Y轴 */
  yAxis?: Axis;

  /** 图表标签 */
  label?: TotalLabel = new TotalLabel(true, undefined, -14);

  /** 图表主区域 */

  /** 图表滚动条 */

  /** 图表缩略轴 */
}

/**
 * 图标配置类型
 */
export type ChartOptionsType = BarOptions | LineOptions;
