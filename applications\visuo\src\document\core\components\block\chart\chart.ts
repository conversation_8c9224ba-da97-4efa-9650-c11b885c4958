import type { BarOptions, Block, Chart, ChartOptions } from '../../../models';
import { defineComponent, watch, type PropType, ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { useChart } from '../../../hooks';
import { debounce } from 'lodash-es';

/**
 * 图表
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-chart',
  props: {
    widget: {
      type: Object as PropType<Chart>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    },
    widgetData: {
      type: Object as PropType<any>,
      default: () => ({}),
      required: true
    }
  },
  setup(props) {
    const containerRef = ref<HTMLElement>();
    const { init, configureChart, render, updateData, resize, updateChartConfig, chart } = useChart();

    const options = computed(() => props.widget.options as ChartOptions & BarOptions);

    const setupChart = async () => {
      if (!containerRef.value) {
        console.warn('图表容器未找到');
        return;
      }

      try {
        // 初始化图表实例
        await init(containerRef.value!);

        // 配置图表
        await configureChart(props.widgetData || []);

        // 渲染可视化
        await render();
      } catch (error) {
        console.error('图表初始化失败:', error);
      }
    };

    onMounted(async () => {
      await nextTick();
      await setupChart();

      // 设置容器尺寸变化监听
      setupResizeObserver();
    });

    // #region ---------创建所有监听器---------

    // 存储监听器的引用，用于组件卸载时销毁
    const watchers: Array<() => void> = [];

    /**
     * 创建监听器并添加到监听器数组
     */
    const createWatcher = (source: any, callback: (value: any, oldValue: any) => void, options: any = {}) => {
      const watcher = watch(source, callback, { deep: true, ...options });
      watchers.push(watcher);
      return watcher;
    };

    // ResizeObserver 监听容器实际尺寸变化
    let resizeObserver: ResizeObserver | null = null;

    const setupResizeObserver = () => {
      if (!containerRef.value || typeof ResizeObserver === 'undefined') {
        return;
      }
      resizeObserver = new ResizeObserver(() => {
        const width = containerRef.value?.clientWidth;
        const height = containerRef.value?.clientHeight;
        resize(width, height);
      });

      resizeObserver.observe(containerRef.value);
    };

    // 清理 ResizeObserver
    const cleanupResizeObserver = () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
        resizeObserver = null;
      }
    };

    createWatcher(
      () => props.widgetData,
      (newVal, oldVal) => {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          updateData();
        }
      }
    );

    createWatcher(
      () => options.value,
      debounce(() => {
        if (options.value) {
          updateChartConfig(options.value, true);
        }
      }, 100)
    );
    // #endregion ---------创建所有监听器---------

    // 组件卸载时销毁所有监听器
    onUnmounted(() => {
      watchers.forEach((stopWatcher) => {
        if (typeof stopWatcher === 'function') {
          stopWatcher();
        }
      });
      watchers.length = 0; // 清空数组

      // 清理 ResizeObserver
      cleanupResizeObserver();
    });

    const getChartInstance = () => {
      return chart.value;
    };

    return {
      containerRef,
      getChartInstance
    };
  }
});
