import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { FormRuleType, SelectPicker, useDocumentStore, type SelectPickerOptions } from '@vis/document-core';
import { useWidget } from '../../../../../../../hooks';
import { VisFormLabel, VisFormFix, VisFormRules, VisFormOption, VisFormBoxStyle } from '../common';
import type { QSelectOption } from 'quasar';

/**
 * 下拉选择配置项
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-select-picker-option',
  components: {
    VisFormLabel,
    VisFormFix,
    VisFormRules,
    VisFormOption,
    VisFormBoxStyle
  },
  props: {
    options: {
      type: Object as PropType<SelectPickerOptions>,
      required: true
    },
    block: {
      type: Object as PropType<SelectPicker>
    }
  },
  setup(props) {
    const { handleManage } = useWidget();

    const computedOptions = computed(() => props.options);
    const isMultiple = computed(() => computedOptions.value.mode === 'multiple');
    watch(
      () => isMultiple.value,
      (val) => {
        if (val && !Array.isArray(computedOptions.value.defaultValue)) {
          computedOptions.value.defaultValue = computedOptions.value.defaultValue
            ? [computedOptions.value.defaultValue]
            : [];
        }
        if (!val && Array.isArray(computedOptions.value.defaultValue)) {
          computedOptions.value.defaultValue = computedOptions.value.defaultValue[0];
        }
      }
    );

    const pickerOptions = ref<QSelectOption[]>([]);

    const noSelected = computed(() => {
      if (Array.isArray(computedOptions.value.defaultValue)) {
        return computedOptions.value.defaultValue.length === 0;
      } else {
        return !computedOptions.value.defaultValue;
      }
    });

    const modeOptions = [
      { label: '单选', value: 'single' },
      { label: '多选', value: 'multiple' }
    ];

    const labelManage = () => {
      handleManage(computedOptions.value, 'label');

      if (computedOptions.value.label) {
        computedOptions.value.label.text = '下拉框';
      }
    };

    const ruleList = [FormRuleType.Required];

    // #region 处理数据
    const { widgetStore } = useDocumentStore();
    const loadData = async () => {
      if (!props.block?.id) return;

      const data = widgetStore.value[props.block.id]?.data;
      if (!data) return;

      const [label, value] = Object.keys(data[0]);
      if (!label) return;

      if (!value) {
        const arr: string[] = [];
        data.forEach((item: any) => {
          if (arr.includes(item[label])) return;

          arr.push(item[label]);
          pickerOptions.value.push({
            label: item[label],
            value: item[label]
          });
        });
      } else {
        pickerOptions.value = data.map((item: any) => {
          return {
            label: item[label],
            value: item[value]
          };
        });
      }
    };

    loadData();

    // #endregion

    return {
      computedOptions,
      isMultiple,
      pickerOptions,
      noSelected,
      modeOptions,

      labelManage,

      ruleList
    };
  }
});
