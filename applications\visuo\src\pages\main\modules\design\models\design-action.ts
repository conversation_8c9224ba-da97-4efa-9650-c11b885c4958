import { Keyboard } from '@hetu/platform-shared';

/**
 * 设计器操作
 */
export class DesignAction {
  /**
   * KEY
   */
  name: string;
  /**
   * 名称
   */
  title: string;
  /**
   * 快捷键
   */
  shortcuts: Array<Keyboard | string | Keyboard[] | string[] | (Keyboard | string)[]>;
  /**
   * 分组
   */
  group = '';
  /**
   * 图标
   */
  icon = '';
  /**
   * 是否禁用
   */
  disable = true;
  /**
   * 是否选中
   */
  active = false;
  /**
   * 是否二次确认
   * */
  confirm = false;
  /**
   * 是否加载中
   */
  loading = false;

  /**
   * 转换为Record中的元素
   */
  get record(): Record<string, DesignAction> {
    const record: Record<string, DesignAction> = {};
    record[this.name] = this;
    return record;
  }

  constructor(
    name: string,
    title: string,
    shortcuts: Array<Keyboard | string | Keyboard[] | string[] | (Keyboard | string)[]>,
    icon: string,
    active?: boolean,
    group?: string,
    disable?: boolean,
    confirm?: boolean,
    loading?: boolean
  ) {
    this.name = name;
    this.title = title;
    this.shortcuts = shortcuts;
    this.icon = icon;
    active && (this.active = active);
    group && (this.group = group);
    typeof disable === 'boolean' && (this.disable = disable);
    typeof confirm === 'boolean' && (this.confirm = confirm);
    typeof loading === 'boolean' && (this.loading = loading);
  }
}

/**
 * 操作分组
 * <AUTHOR>
 */
export class DesignActionGroup {
  /**
   * KEY
   */
  name: string;
  /**
   * 名称
   */
  title: string;
  /**
   * 图标
   */
  icon = '';
  /**
   * 操作集
   */
  actions: Record<string, DesignAction> = {};
  /**
   * 是否禁用
   */
  disable = true;

  /**
   * 转换为Record中的元素
   */
  get record(): Record<string, DesignActionGroup> {
    const record: Record<string, DesignActionGroup> = {};
    record[this.name] = this;
    return record;
  }

  constructor(name: string, title: string, icon?: string | boolean, disable?: boolean) {
    this.name = name;
    this.title = title;
    if (typeof icon === 'boolean') {
      disable = icon;
    } else {
      icon && (this.icon = icon);
    }
    typeof disable === 'boolean' && (this.disable = disable);
  }
}

/**
 * 快捷键分组
 * <AUTHOR>
 * */
export class ShortcutGroup {
  /**
   * 分组名称
   */
  name: string;
  /**
   * 分组标题
   */
  title: string;
  /**
   * 操作集
   */
  actions: Record<string, DesignAction> = {};
  /**
   * 转换为Record中的元素
   */
  get record(): Record<string, ShortcutGroup> {
    const record: Record<string, ShortcutGroup> = {};
    record[this.name] = this;
    return record;
  }

  constructor(name: string, title: string) {
    this.name = name;
    this.title = title;
  }
}
