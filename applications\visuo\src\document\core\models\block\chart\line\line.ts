import { Adorn, Color, Effects, EffectsType, FillPaints, Stroke, StrokeType } from '../../../ui';
import { Axis, AxisGrid, AxisLine } from '../axis';
import { ChartOptions } from '../chart';
import { BaseMark, MarkSymbol } from '../mark';

/**
 * 折线图标记
 * <AUTHOR>
 */
export class LineMark extends BaseMark {
  /**
   * 类型
   */
  type = 'line';

  /**
   * 线类型
   */
  interpolate: Interpolate = Interpolate.Line;

  /**
   * 是否显示标记
   */
  visible = true;

  /**
   * 标记形状
   */
  shape: MarkSymbol = new MarkSymbol();

  /**
   * 标记颜色填充
   */
  shapeFillPaints: FillPaints = new FillPaints(undefined, new Color());

  /**
   * 标记描边
   */
  shapeStork?: Stroke = new Stroke(undefined, undefined, StrokeType.None);

  /**
   * 标记特效
   */
  shapeEffects: Effects = new Effects(false);
}

/**
 * 折线图配置
 */
export class LineOptions extends ChartOptions {
  mark: LineMark = new LineMark(new Stroke([1, 1, 1, 1], new FillPaints(undefined, new Color(58, 124, 255, 1))));

  /** x轴 */
  xAxis: Axis = new Axis(new AxisLine(0, new Stroke([0, 0, 0, 0])), new AxisGrid(0));

  /** y轴 */
  yAxis: Axis = new Axis(
    new AxisLine(0, new Stroke([0, 0, 0, 0])),
    new AxisGrid(1, new Stroke([1, 1, 1, 1], new FillPaints(undefined, new Color(0, 0, 0, 0.5)), StrokeType.Dashed))
  );
}

/**
 * 线类型
 */
export enum Interpolate {
  /** 折线 */
  Line = 'line',
  /** 曲线 */
  Curve = 'curve',
  /** 阶梯 */
  Step = 'step'
}
