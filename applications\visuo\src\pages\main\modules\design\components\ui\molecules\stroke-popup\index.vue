<template>
  <div class="vis-stroke-popup">
    <div @click.stop="showPopup">
      <slot name="btn" :active="popupShow">
        <q-btn class="vis-btn" :class="{ active: popupShow }">
          <slot v-if="$slots.icon" name="icon"></slot>
          <span v-else>边框</span>
        </q-btn>
      </slot>
    </div>
    <vis-popup class="vis-popup" title="边框" ref="popupRef" :target="false" @hide="popupShow = false">
      <!-- 边框 -->
      <template #content>
        <div class="vis-graph-popup-padding !pb-1">
          <vis-stroke-style v-model="computedStroke" />

          <template v-if="computedStroke.style !== StrokeType.None">
            <vis-stroke-width v-model="computedStroke.position" :positionIndex="positionIndex" />
            <q-separator />
            <vis-color-selector class="pt-3" v-model="computedStroke.fillPaints" title="边框颜色" />
            <q-separator />
            <vis-custom-color class="pt-3" v-model="computedStroke.fillPaints" colorType="stroke"></vis-custom-color>
          </template>
        </div>
      </template>
    </vis-popup>
  </div>
</template>
<script src="./index.ts" lang="ts"></script>
