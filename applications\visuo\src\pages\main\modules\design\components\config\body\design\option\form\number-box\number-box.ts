import { computed, defineComponent, type PropType } from 'vue';
import { FormRuleType, Label, type NumberBoxOptions } from '@vis/document-core';
import { useWidget } from '../../../../../../../hooks';
import { VisFormLabel, VisFormFix, VisFormRules, VisFormBoxStyle } from '../common';
import { isNumber } from 'lodash-es';
/**
 * 数字输入组件属性面板
 * @authot guohuizheng
 */
export default defineComponent({
  name: 'vis-config-number-box-option',
  components: {
    VisFormLabel,
    VisFormFix,
    VisFormRules,
    VisFormBoxStyle
  },
  props: {
    options: {
      type: Object as PropType<NumberBoxOptions>,
      required: true
    }
  },
  setup(props) {
    const { handleManage } = useWidget();

    const computedOptions = computed(() => props.options);

    // 格式列表
    const formatOptions = [
      { value: 'number', label: '数字' },
      { value: 'percent', label: '百分比' }
    ];

    const labelManage = () => {
      handleManage(computedOptions.value, 'label');

      if (computedOptions.value.label) {
        computedOptions.value.label.text = '数值输入';
      }
    };

    // 前后缀(按照前缀图标、前缀文本、后缀图标、后缀文本，依次添加)
    const fixList = [true, true, true, true];

    // 可添加的校验规则列表
    const ruleList = [FormRuleType.Required, FormRuleType.Range, FormRuleType.Pattern];

    // 数值范围
    const rangeRule = computed(() => {
      return computedOptions.value.rules?.find((item) => item.type === FormRuleType.Range);
    });
    const minValue = computed(() => {
      return rangeRule.value && isNumber(Number(rangeRule.value.pattern?.[0]))
        ? Number(rangeRule.value.pattern?.[0])
        : -Infinity;
    });
    const maxValue = computed(() => {
      return rangeRule.value && isNumber(Number(rangeRule.value.pattern?.[1]))
        ? Number(rangeRule.value.pattern?.[1])
        : Infinity;
    });

    const handleCheck = () => {
      if (!computedOptions.value.defaultValue) return;

      if (computedOptions.value.defaultValue < minValue.value) {
        computedOptions.value.defaultValue = minValue.value;
      }
      if (computedOptions.value.defaultValue > maxValue.value) {
        computedOptions.value.defaultValue = maxValue.value;
      }
    };

    return {
      computedOptions,
      formatOptions,

      labelManage,

      fixList,
      ruleList,

      minValue,
      maxValue,
      handleCheck
    };
  }
});
