<template>
  <div class="vis-config-page">
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>页面</span>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-config-page-thumbnail">
          <img v-if="thumbnail" :src="thumbnail" alt="封面" class="w-full h-full object-cover" />
        </div>
        <div class="text-xs text-grey mt-2 mb-3">
          <span>如何设置封面</span>
          <q-tooltip>
            选中容器后点击右键选择设为封面，<br />
            推荐宽高比例16:9
          </q-tooltip>
        </div>
      </div>
    </div>

    <q-separator />

    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>背景</span>
      </div>
      <div class="vis-config-card__body">
        <div class="position-relative">
          <vis-fill popTitle="颜色" v-model="activePage.fillPaints" @change="onChangeFillPaints" />
        </div>
      </div>
    </div>

    <q-separator />

    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>背景</span>
        <q-btn flat dense>
          <ht-icon class="vis-icon" name="vis-control" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-config-page-theme">
          <div class="vis-config-page-theme-title">主题色</div>
          <div class="vis-config-page-theme-body" @click="onThemeChange">
            <template v-for="(color, index) in themes" :key="color">
              <div
                class="vis-config-page-theme-body-color"
                :class="{ 'rounded-l-0.5': index === 0, 'rounded-r-0.5': index === themes.length - 1 }"
                :style="{ 'background-color': color }"
              ></div>
            </template>
          </div>
        </div>
        <div class="vis-config-page-theme">
          <div class="vis-config-page-theme-title">调色板</div>
          <div class="vis-config-page-theme-body gap-2" @click="onPaletteChange">
            <template v-for="color in palettes" :key="color">
              <div
                :class="['vis-config-page-theme-body-palette', { selected: isPaletteSelected(color) }]"
                :style="{ 'background-color': color }"
              ></div>
            </template>
          </div>
        </div>
        <div class="vis-config-page-theme">
          <div class="vis-config-page-theme-title">文本样式</div>
          <vis-text :option="text" mode="base" @change="onChangeText" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss" scoped></style>
